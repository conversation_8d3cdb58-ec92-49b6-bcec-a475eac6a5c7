import { PrismaClient, ItemType } from './app/generated/prisma/client';

const prisma = new PrismaClient();

async function main() {
  // Clear existing data (optional, for development)
  await prisma.saleItem.deleteMany();
  await prisma.sale.deleteMany();
  await prisma.stockMovement.deleteMany();
  await prisma.inventory.deleteMany();
  await prisma.resident.deleteMany();

  // Create inventory items
  const inventoryItems = [
    {
      itemType: ItemType.A_BUILDING_CARD,
      current: 16,
      price: 200,
      minStock: 5,
      maxStock: 50
    },
    {
      itemType: ItemType.B_BUILDING_CARD,
      current: 20,
      price: 200,
      minStock: 5,
      maxStock: 50
    },
    {
      itemType: ItemType.ALL_AREA_CARD,
      current: 4,
      price: 200,
      minStock: 2,
      maxStock: 20
    },
    {
      itemType: ItemType.MAIN_GATE_REMOTE_CONTROL,
      current: 4,
      price: 700,
      minStock: 1,
      maxStock: 10
    }
  ];

  for (const item of inventoryItems) {
    const inventory = await prisma.inventory.create({
      data: item
    });

    // Create initial stock movement record
    await prisma.stockMovement.create({
      data: {
        inventoryId: inventory.id,
        type: 'INITIAL',
        quantity: item.current,
        reason: 'Initial stock setup',
        previousStock: 0,
        newStock: item.current
      }
    });
  }

  console.log('✅ Database seeded successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });