"use server";

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { BalanceType } from "@prisma/client";
import { Prisma } from "@prisma/client";

interface LastDayBalance {
  date: string;
  balance: number;
  isLastDayOfMonth: boolean;
  fullDateTime?: string;
  rawData?: {
    交易日期: string;
    帳務日期: string;
    說明: string;
    提出: string;
    存入: string;
    餘額: string;
    交易資訊: string;
    備註: string;
  };
}

export async function storeLastDayBalances({
  originalId,
  balances,
  rawData,
}: {
  originalId: string;
  balances: Record<string, LastDayBalance>;
  rawData: Record<string, any>[];
}) {
  try {
    // Validate input data
    if (!originalId) {
      throw new Error("originalId is required");
    }
    if (!balances || Object.keys(balances).length === 0) {
      throw new Error("balances object is required and must not be empty");
    }
    if (!Array.isArray(rawData)) {
      throw new Error("rawData must be an array");
    }
    if (rawData.length === 0) {
      console.warn("Warning: rawData array is empty");
    }

    // Validate rawData structure if present
    if (rawData.length > 0) {
      const firstEntry = rawData[0];
      if (!firstEntry?.餘額) {
        throw new Error("Invalid rawData structure: missing 餘額 field");
      }
      if (typeof firstEntry.餘額 !== "string") {
        throw new Error("Invalid rawData structure: 餘額 must be a string");
      }
    }

    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Extract just the first sequence of digits
    const extractAccountNumber = (text: string) => {
      const match = text.match(/^\d+/); // Match digits at the start
      return match ? match[0] : text.replace(/\D/g, '');
    };

    const cleanOriginalId = extractAccountNumber(originalId);
    console.log("Original:", originalId, "Cleaned:", cleanOriginalId);

    const bankAccount = await prisma.bankAccount.findFirst({
      where: {
        userId,
        accountType: "BANK",
        OR: [
          {
            originalId: cleanOriginalId
          },
          {
            originalPayload: {
              path: ["description"],
              equals: originalId
            }

          }
        ]
      },
    });

    //console.log("bankAccount, userId, balances, rawData:", {bankAccount, userId, balances, rawData})
    //console.log("balance", JSON.stringify(balances, null, 2))

    if (!bankAccount) {
      throw new Error(`Bank account not found for originalId: ${cleanOriginalId}`);
    }

    // Process balance records
    for (const [monthYear, monthBalance] of Object.entries(balances)) {
      // Validate balance object structure
      if (!monthBalance.date || !monthBalance.balance) {
        console.warn(`Skipping invalid balance record for ${monthYear}:`, monthBalance);
        continue;
      }

      // Validate and parse date
      const dateParts = monthBalance.date.split("/");
      if (dateParts.length !== 3) {
        throw new Error(`Invalid date format for ${monthYear}: ${monthBalance.date}`);
      }

      const [year, month, day] = dateParts;
      const parsedYear = parseInt(year);
      const parsedMonth = parseInt(month) - 1;
      const parsedDay = parseInt(day);

      // Validate date components
      if (isNaN(parsedYear) || isNaN(parsedMonth) || isNaN(parsedDay)) {
        throw new Error(`Invalid date components for ${monthYear}: ${monthBalance.date}`);
      }      
      // Parse time components from fullDateTime (Taipei time UTC+8)
      let hours = 0, minutes = 0;
        if (monthBalance.fullDateTime) {
        // Parse "2025/06/04 14:39" format
        const [_, time] = monthBalance.fullDateTime.split(' ');
        if (time) {
          const [h, m] = time.split(':');
          const parsedHours = parseInt(h);
          const parsedMinutes = parseInt(m);
          
          if (!isNaN(parsedHours) && !isNaN(parsedMinutes)) {
            // Convert Taipei time to UTC (subtract 8 hours)
            hours = parsedHours >= 8 ? parsedHours - 8 : parsedHours + 16;
            minutes = parsedMinutes;
          }
        }
      }

      const date = new Date(Date.UTC(parsedYear, parsedMonth, parsedDay, hours, minutes, 0));
      console.log("Converting Taipei time", monthBalance.fullDateTime, "to UTC:", date.toISOString());
      console.log("date", date)

      // Validate parsed date
      if (isNaN(date.getTime())) {
        throw new Error(`Invalid date generated for ${monthYear}: ${monthBalance.date}`);
      }

      // Skip non-last day of month records
      //if (!monthBalance.isLastDayOfMonth) continue;
      
      // Find the matching rawData entry for this month
      const rawEntry = monthBalance.rawData || null;

      // Parse and validate balance amount
      let cleanBalance: number;
      try {
        if (rawEntry?.餘額) {
          const rawBalance = rawEntry.餘額.replace(/,/g, "");
          cleanBalance = parseFloat(rawBalance);
          if (isNaN(cleanBalance)) {
            throw new Error(`Invalid balance amount in rawData: ${rawEntry.餘額}`);
          }
        } else {
          cleanBalance = monthBalance.balance;
          if (typeof cleanBalance !== "number" || isNaN(cleanBalance)) {
            throw new Error(`Invalid balance amount in monthBalance: ${monthBalance.balance}`);
          }
        }
      } catch (error) {
        console.error(`Error parsing balance for ${monthYear}:`, error);
        throw error;
      }      
      // Check for existing balance record on the same date
      const existingBalance = await prisma.balance.findFirst({
        where: {
          accountId: bankAccount.id,
          date: {
            gte: new Date(Date.UTC(parsedYear, parsedMonth, parsedDay, 0, 0, 0)),
            lt: new Date(Date.UTC(parsedYear, parsedMonth, parsedDay + 1, 0, 0, 0))
          }
        },
        orderBy: {
          date: 'desc'
        }
      });

      if (existingBalance) {
        // If existing record is newer than our current record, skip this update
        if (existingBalance.date > date) {
          console.log(`Skipping update for ${monthBalance.date} as newer record exists from ${existingBalance.date}`);
          continue;
        }

        // Update existing record if it's from the same date
        if (existingBalance.date.getTime() === date.getTime()) {
          await prisma.balance.update({
            where: { id: existingBalance.id },
            data: {
              amount: cleanBalance,
              description: `${monthBalance.date}餘額`,
              type: BalanceType.AVAILABLE,
              currencyIso: "TWD",
              originalPayload: rawEntry || Prisma.JsonNull
                ? (rawEntry as Prisma.InputJsonValue)
                : Prisma.JsonNull,
              updatedAt: new Date(),
            },
          });
          console.log(`Updated existing balance record for ${monthYear} with amount ${cleanBalance}`);
        }
      } else {
        // Create new record if none exists
        await prisma.balance.create({
          data: {
            amount: cleanBalance,
            date,
            description: `${monthBalance.date}餘額`,
            type: BalanceType.AVAILABLE,
            currencyIso: "TWD",
            originalPayload: rawEntry || Prisma.JsonNull
              ? (rawEntry as Prisma.InputJsonValue)
              : Prisma.JsonNull,
            accountId: bankAccount.id,
          },
        });
        console.log(`Created new balance record for ${monthYear} with amount ${cleanBalance}`);
      }

      console.log(`Successfully created balance record for ${monthYear} with amount ${cleanBalance}`);
    }

    return { success: true };
  } catch (error) {
    console.error("Error storing last day balances:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      details: error instanceof Error ? error.stack : undefined,
    };
  }
}
