"use client"

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { 
  createPaymentRequest, 
  createPayment, 
  updatePayment, 
  deletePayment,
  createBankAccount,
  updateBankAccount,
  deleteBankAccount
} from '@/actions/payments/payment-requests'
import { PaymentAccountType } from "../../prisma/app/generated/prisma/client";

export { PaymentAccountType } from '../../prisma/app/generated/prisma/client'

export interface PaymentAccount {
  id: string
  accountType: PaymentAccountType
  bankCode: string
  bankName: string
  branchName: string
  accountNumber: string
  accountName: string
  isDefault: boolean
  isActive: boolean
  paymentRequestId: string | null
  createdAt: Date
  updatedAt: Date
}

export interface PaymentRequest {
  id: string
  name: string
  year: string
  month: string
  paymentAccounts: PaymentAccount[]
  payments: Payment[]
  createdAt: Date
  updatedAt: Date
}

export interface Payment {
  id: string
  sequenceId: string
  accountingSubject: string
  payee: string
  month: string
  amount: number
  paymentMethod: string
  showAccountInfo: boolean

  remitterAccountId?: string | null
  payeeAccountId?: string | null
  remitterAccount?: PaymentAccount | null
  payeeAccount?: PaymentAccount | null

  paymentRequestId?: string | null
  paymentRequest?: PaymentRequest | null
  createdAt: Date
  updatedAt: Date
}

// Fetch payment requests
export function usePaymentRequests() {
  return useQuery({
    queryKey: ['paymentRequests'],
    queryFn: async (): Promise<PaymentRequest[]> => {
      const response = await fetch('/api/payment-requests')
      if (!response.ok) {
        throw new Error('Failed to fetch payment requests')
      }
      return response.json()
    },
  })
}

// Create payment request mutation
export function useCreatePaymentRequest() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: createPaymentRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

// Bank account mutations
export function useCreateBankAccount() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: createBankAccount,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

export function useUpdateBankAccount() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => updateBankAccount(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

export function useDeleteBankAccount() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: deleteBankAccount,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

// Payment mutations
export function useCreatePayment() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: createPayment,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

export function useUpdatePayment() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => updatePayment(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

export function useDeletePayment() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: deletePayment,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

// Toggle account info mutation
export function useToggleAccountInfo() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ paymentId, show }: { paymentId: string; show: boolean }) => {
      // Fetch the existing payment to get all required fields. Use a type assertion to ensure it's treated as an array.
      const existingPayment = (queryClient.getQueryData(['paymentRequests']) as PaymentRequest[] | undefined)?.flatMap((pr: PaymentRequest) => pr.payments).find((p: Payment) => p.id === paymentId);

      if (!existingPayment) {
        throw new Error('Payment not found for update');
      }
      // Only send the field that is being updated
      const result = await updatePayment(paymentId, { 
        ...existingPayment, // Spread existing properties
        amount: existingPayment.amount, // Ensure amount is passed as number
        showAccountInfo: show, // Update the specific field
        // Explicitly set optional fields to undefined if they are null in existingPayment
      })
      return result
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}
