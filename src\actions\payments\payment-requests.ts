'use server'

import { prisma } from '@/lib/db'
import { revalidatePath } from 'next/cache'
import { PaymentAccountType } from '../../../prisma/app/generated/prisma/client'

export async function createPaymentRequest(data: {
  name: string
  year: string
  month: string
}) {
  try {
    const paymentRequest = await prisma.paymentRequest.create({
      data,
      include: {
        payments: {
          include: {
            remitterAccount: true,
            payeeAccount: true
          }
        },
        paymentAccounts: true
      }
    })
    
    revalidatePath('/payment-manager')
    return { success: true, data: paymentRequest }
  } catch (error: any) {
    console.error('Error creating payment request:', error)
    
    if (error.code === 'P2002') {
      return { success: false, error: 'Payment request for this year and month already exists' }
    }
    
    return { success: false, error: 'Failed to create payment request' }
  }
}

export async function createBankAccount(data: {
  accountType: PaymentAccountType
  bankCode: string
  bankName: string
  branchName: string
  accountNumber: string
  accountName: string
  isDefault: boolean
  paymentRequestId: string
}) {
  try {
    // If this is being set as default, unset other defaults of the same type
    if (data.isDefault) {
      await prisma.paymentAccount.updateMany({
        where: {
          paymentRequestId: data.paymentRequestId,
          accountType: data.accountType,
          isDefault: true
        },
        data: { isDefault: false }
      })
    }

    const paymentAccount = await prisma.paymentAccount.create({
      data
    })

    revalidatePath('/payment-manager')
    return { success: true, data: paymentAccount }
  } catch (error) {
    console.error('Error creating bank account:', error)
    return { success: false, error: 'Failed to create bank account' }
  }
}

export async function updateBankAccount(id: string, data: {
  accountType: PaymentAccountType
  bankCode: string
  bankName: string
  branchName: string
  accountNumber: string
  accountName: string
  isDefault: boolean
}) {
  try {
    // If this is being set as default, unset other defaults of the same type
    if (data.isDefault) {
      const currentAccount = await prisma.paymentAccount.findUnique({
        where: { id },
        select: { paymentRequestId: true }
      })

      if (currentAccount?.paymentRequestId) {
        await prisma.paymentAccount.updateMany({
          where: {
            paymentRequestId: currentAccount.paymentRequestId,
            accountType: data.accountType,
            isDefault: true,
            NOT: { id }
          },
          data: { isDefault: false }
        })
      }
    }

    const paymentAccount = await prisma.paymentAccount.update({
      where: { id },
      data
    })

    revalidatePath('/payment-manager')
    return { success: true, data: paymentAccount }
  } catch (error) {
    console.error('Error updating bank account:', error)
    return { success: false, error: 'Failed to update bank account' }
  }
}

export async function deleteBankAccount(id: string) {
  try {
    await prisma.paymentAccount.delete({
      where: { id }
    })

    revalidatePath('/payment-manager')
    return { success: true }
  } catch (error) {
    console.error('Error deleting bank account:', error)
    return { success: false, error: 'Failed to delete bank account' }
  }
}

export async function createPayment(data: {
  sequenceId: string
  accountingSubject: string
  payee: string
  month: string
  amount: number
  paymentMethod: string
  showAccountInfo: boolean
  paymentRequestId: string
  remitterAccountId?: string
  payeeAccountId?: string
}) {
  try {
    const payment = await prisma.payment.create({
      data: {
        ...data,
        amount: Math.round(data.amount * 100), // Store in cents
      },
      include: {
        remitterAccount: true,
        payeeAccount: true
      }
    })

    revalidatePath('/payment-manager')
    return { 
      success: true, 
      data: {
        ...payment,
        amount: payment.amount / 100 // Convert back to dollars
      }
    }
  } catch (error) {
    console.error('Error creating payment:', error)
    return { success: false, error: 'Failed to create payment' }
  }
}

export async function updatePayment(id: string, data: {
  sequenceId: string
  accountingSubject: string
  payee: string
  month: string
  amount: number
  paymentMethod: string
  showAccountInfo: boolean
  remitterAccountId?: string | null
  payeeAccountId?: string | null
}) {
  try {
    const payment = await prisma.payment.update({
      where: { id },
      data: {
        ...data,
        amount: Math.round(data.amount * 100), // Store in cents
      },
      include: {
        remitterAccount: true,
        payeeAccount: true
      }
    })

    revalidatePath('/payment-manager')
    return { 
      success: true, 
      data: {
        ...payment,
        amount: payment.amount / 100 // Convert back to dollars
      }
    }
  } catch (error) {
    console.error('Error updating payment:', error)
    return { success: false, error: 'Failed to update payment' }
  }
}

export async function deletePayment(id: string) {
  try {
    await prisma.payment.delete({
      where: { id }
    })

    revalidatePath('/payment-manager')
    return { success: true }
  } catch (error) {
    console.error('Error deleting payment:', error)
    return { success: false, error: 'Failed to delete payment' }
  }
}