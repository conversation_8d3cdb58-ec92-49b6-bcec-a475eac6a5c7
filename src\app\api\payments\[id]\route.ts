import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const {
      sequenceId,
      accountingSubject,
      payee,
      month,
      amount,
      paymentMethod,
      showAccountInfo,
      remitterAccountId,
      payeeAccountId
    } = await request.json()

    const payment = await prisma.payment.update({
      where: { id: params.id },
      data: {
        sequenceId,
        accountingSubject,
        payee,
        month,
        amount,
        paymentMethod,
        showAccountInfo,
        remitterAccountId,
        payeeAccountId
      }
    })

    return NextResponse.json(payment)
  } catch (error) {
    console.error('Error updating payment:', error)
    return NextResponse.json(
      { error: 'Failed to update payment' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.payment.delete({
      where: { id: params.id }
    })

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error('Error deleting payment:', error)
    return NextResponse.json(
      { error: 'Failed to delete payment' },
      { status: 500 }
    )
  }
}