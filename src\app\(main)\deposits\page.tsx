"use client";

import {
  useState,
  ChangeEvent,
  FormEvent,
  ReactNode,
  useEffect,
  useMemo,
} from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "sonner";
import {
  fetchManagementSheetData,
} from "@/lib/google/fetch-managements-from-sheets";
import { uploadDepositToSheets } from "@/lib/google/upload-deposit-to-sheets";
import {
  Loader2,
  FileText,
  CheckCircle,
  XCircle,
  ExternalLink,
  RefreshCw,
  TableIcon,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ReminderLetterDialog } from "@/components/deposits/reminder-letter-dialog";
import { usePdfFont } from "@/hooks/use-pdf-font";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";

// Properly initialize pdfMake with fonts
pdfMake.vfs = (pdfFonts as any).pdfMake?.vfs || pdfFonts;

type StatusType = "idle" | "loading" | "success" | "error";

interface ProcessStatus {
  type: StatusType;
  message: string | ReactNode;
  spreadsheetUrl?: string;
  googleDriveLink?: string;
}

interface SheetDisplayData {
  headers: (string | number | null)[]; // Allow headers to be string, number, or null
  rows: (string | number | null)[][];
}

export default function UploadPage() {
  const { isSuccess: isFontLoaded, isLoading: isFontLoading } = usePdfFont();
  const [files, setFiles] = useState<File[]>([]);
  const [uploadStatus, setUploadStatus] = useState<ProcessStatus>({
    type: "idle",
    message: "",
  });
  const [enableUpdate, setEnableUpdate] = useState({
    value: false,
    onChange: (v: boolean) => setEnableUpdate({ value: v, onChange: enableUpdate.onChange }),
  });
  const [targetYear, setTargetYear] = useState<string>(new Date().getFullYear().toString());
  const [sheetData, setSheetData] = useState<SheetDisplayData | null>(null);
  const [nameMap, setNameMap] = useState<Map<string, string> | null>(null);
  const [isFetchingSheet, setIsFetchingSheet] = useState<boolean>(false);
  const [fetchSheetError, setFetchSheetError] = useState<string | null>(null);
  const [isUploadOpen, setIsUploadOpen] = useState<boolean>(false);
  const [autoPayFiles, setAutoPayFiles] = useState<File[]>([]);
  const [fileUploadStatus, setFileUploadStatus] = useState<ProcessStatus>({
    type: "idle",
    message: "",
  });
  const [unpaidDialogOpen, setUnpaidDialogOpen] = useState(false);
  const [unpaidList, setUnpaidList] = useState<string[]>([]);
  const [unpaidMonth, setUnpaidMonth] = useState<string>("");

  const currentYear = new Date().getFullYear()
  const yearOptions = Array.from({ length: 50 }, (_, i) => currentYear - i)

  // Add state for dialog and data
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState<
    |"depositsUnmatched"
    |"depositsSmallAmount"
    |"depositsSpecial"
    |"withdrawals"
    |"withdrawalsUnmatched"
    |"motorBikeParkingLot"
    | null
  >(null);
  const [dialogData, setDialogData] = useState<any[]>([]);
  const [overdueDialogOpen, setOverdueDialogOpen] = useState(false);
  const [overdueList, setOverdueList] = useState<
    Array<{
      id: string;
      amount: string;
      months: string;
    }>
  >([]);
  const [overdueMonthThreshold, setOverdueMonthThreshold] = useState<number>(2);
  const [isReminderLetterDialogOpen, setIsReminderLetterDialogOpen] = useState(false);

  const reminderSheetData = useMemo(() => {
    if (!sheetData) return null;
    if (!nameMap) return sheetData; // Return original data if map is not ready

    const nameHeader = "姓名";
    const headers = [...sheetData.headers];
    const nameColIdx = headers.indexOf(nameHeader);

    if (nameColIdx !== -1) {
        // If name column already exists, just use it
        return sheetData;
    }

    headers.push(nameHeader);
    const newNameColIdx = headers.length - 1;
    const seqIdColIdx = sheetData.headers.findIndex(h => String(h) === "序號");

    if (seqIdColIdx === -1) {
        console.warn("Cannot merge names, '序號' column not found.");
        return sheetData; // Cannot merge without seqId
    }

    const rows = sheetData.rows.map(row => {
      const newRow = [...row];
      const seqId = String(row[seqIdColIdx]);
      const name = nameMap.get(seqId) || "";
      newRow[newNameColIdx] = name;
      return newRow;
    });

    return { headers, rows };
  }, [sheetData, nameMap]);

  const totalOverdueAmount = useMemo(() => {
    return overdueList.reduce((sum, item) => {
      const amount = parseFloat(item.amount.replace(/,/g, ""));
      return sum + (isNaN(amount) ? 0 : amount);
    }, 0);
  }, [overdueList]);

  const totalOverdueMonths = useMemo(() => {
    return overdueList.reduce((sum, item) => {
      const months = parseFloat(item.months);
      return sum + (isNaN(months) ? 0 : months);
    }, 0);
  }, [overdueList]);

  const convertToCSV = (headers: (string | number | null)[], rows: (string | number | null)[][]): string => {
    const escapeCSV = (field: any): string => {
      if (field === null || typeof field === 'undefined') {
        return '';
      }
      const stringField = String(field);
      // Check if field contains comma, newline, or double quote
      if (stringField.includes(',') || stringField.includes('\n') || stringField.includes('"')) {
        // Escape double quotes by replacing them with two double quotes, then wrap in double quotes
        return `"${stringField.replace(/"/g, '""')}"`;
      }
      return stringField;
    };

    const headerRow = headers.map(escapeCSV).join(',');
    const dataRows = rows.map(row => row.map(escapeCSV).join(','));
    
    return [headerRow, ...dataRows].join('\n');
  };

  const handleDownloadCSV = () => {
    if (!sheetData || !sheetData.headers || !sheetData.rows || sheetData.rows.length === 0) {
      toast.error("沒有可供下載的資料。");
      return;
    }

    try {
      const csvString = convertToCSV(sheetData.headers, sheetData.rows);
      const blob = new Blob(['\uFEFF' + csvString], { type: 'text/csv;charset=utf-8;' }); // Prepend BOM for Excel
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", "社區管理費繳費統計表資料表.csv");
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast.success("CSV下載成功！");
    } catch (error) {
      console.error("Error generating CSV: ", error);
      toast.error("CSV下載失敗。");
    }
  };

  const handleDownloadPDF = async () => {
    if (!sheetData || !sheetData.headers || !sheetData.rows || sheetData.rows.length === 0) {
      toast.error("沒有可供下載的資料製作PDF。");
      return;
    }
    if (!isFontLoaded) {
      toast.error("Font is not loaded yet. Please try again in a moment.");
      return;
    }

    try {
      // Use default pdfMake fonts and let the system handle Chinese characters
      // This avoids font loading issues while still allowing Chinese text to render
      // The PDF will use the system's default font for Chinese characters
      // VFS is now properly initialized at the top of the file

      // Debug: Log the data structure
      //console.log('Headers:', sheetData.headers);
      //console.log('First few rows:', sheetData.rows.slice(0, 3));

      const tableHeaders = sheetData.headers.map((header, index) => {
        const headerText = header === null || header === undefined ? `Col${index + 1}` : String(header);
        return {
          text: headerText,
          style: 'tableHeader'
        };
      });

      const tableRows = sheetData.rows.map((row, rowIndex) => {
        if (!Array.isArray(row)) {
          console.warn(`Row ${rowIndex} is not an array:`, row);
          return [];
        }

        // Ensure row has same length as headers
        const normalizedRow = [];
        for (let i = 0; i < sheetData.headers.length; i++) {
          const cell = row[i];
          const cellText = cell === null || cell === undefined ? '' : String(cell);
          normalizedRow.push({
            text: cellText
          });
        }
        return normalizedRow;
      }).filter(row => row.length > 0); // Remove empty rows

      // Validate table data before creating PDF
      console.log('Table headers length:', tableHeaders.length);
      console.log('Table rows count:', tableRows.length);
      console.log('First table row length:', tableRows[0]?.length);

      // Ensure all rows have the same length as headers
      const validatedRows = tableRows.map((row, index) => {
        if (row.length !== tableHeaders.length) {
          console.warn(`Row ${index} length (${row.length}) doesn't match headers length (${tableHeaders.length})`);
          // Pad or trim row to match headers length
          const paddedRow = [...row];
          while (paddedRow.length < tableHeaders.length) {
            paddedRow.push({ text: '' });
          }
          return paddedRow.slice(0, tableHeaders.length);
        }
        return row;
      });

      
      const docDefinition: any = {
        pageOrientation: 'landscape',
        pageMargins: [0, 0, 0, 0],
        pageSize: 'A4',
        content: [
          { text: '社區管理費繳費統計表', style: 'header' },
          {
            stack: [
              {
                table: {
                  headerRows: 1,
                  widths: Array(tableHeaders.length).fill('*'),
                  body: [tableHeaders, ...validatedRows],
                },
                layout: 'lightHorizontalLines',
                /*layout: {
                  paddingLeft: function () { return 2; },   // horizontal
                  paddingRight: function () { return 2; },
                  paddingTop: function () { return 1; },    // ↓ reduce top padding
                  paddingBottom: function () { return 1; }  // ↓ reduce bottom padding
                },*/
                style: 'table'
              }
            ],
            pageBreak: 'avoid' // prevent splitting
          }
        ],
        styles: {
          header: {
            fontSize: 10,
            bold: true,
            alignment: 'center',
            margin: [0, 8, 0, 0],
          },
          tableHeader: {
            bold: true,
            fontSize: 7,
            alignment: 'right',
            valign: 'bottom',
            margin: [0, 4, 0, 0],
            // fillColor: '#eeeeee' // Optional: background color for header
          },
          table: {
            alignment: 'right',
            fontSize: 7,
            Padding: 0
          },
        },
        defaultStyle: {
          font: "微軟正黑體"
        }
      };

      pdfMake.createPdf(docDefinition).download('社區管理費繳費統計表資料表.pdf');
      toast.success("PDF產生成功並開始下載。");

    } catch (error: any) {
      console.error("Error generating PDF: ", error);
      toast.error(`PDF產生失敗: ${error.message || "Unknown error"}`);
    }
  };

  // Store all returned items
  const [depositsUnmatched, setDepositsUnmatched] = useState<any[]>([]);
  const [depositsSmallAmount, setDepositsSmallAmount] = useState<any[]>([]);
  const [depositsSpecial, setDepositsSpecial] = useState<any[]>([]);
  const [withdrawals, setWithdrawals] = useState<any[]>([]);
  const [withdrawalsUnmatched, setWithdrawalsUnmatched] = useState<any[]>([]);
  const [motorBikeParkingLot, setMotorBikeParkingLot] = useState<any[]>([]);

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    setUploadStatus({ type: "idle", message: "" });
    if (event.target.files && event.target.files.length > 0) {
      const selectedFiles = Array.from(event.target.files);
      const validFiles = selectedFiles.filter(file => 
        file.type === "text/csv" || file.name.endsWith(".csv")
      );

      if (validFiles.length < selectedFiles.length) {
        toast.error("Some files were not CSVs and have been ignored.");
      }
      
      setFiles(validFiles);

      if (validFiles.length === 0) {
        event.target.value = ""; // Clear input if no valid files
      }
    } else {
      setFiles([]);
    }
  };

  // Helper to save to localStorage (1 year)
  const saveToLocalStorage = (key: string, value: any) => {
    const data = { value, expires: Date.now() + 365 * 24 * 60 * 60 * 1000 };
    localStorage.setItem(key, JSON.stringify(data));
  };
  // Helper to load from localStorage
  const loadFromLocalStorage = (key: string) => {
    const raw = localStorage.getItem(key);
    if (!raw) return null;
    try {
      const data = JSON.parse(raw);
      if (data.expires && data.expires > Date.now()) return data.value;
      localStorage.removeItem(key);
      return null;
    } catch {
      return null;
    }
  };

  const getOverdueAccounts = (threshold: number) => {
    if (!sheetData) return [];

    const monthsColIdx = sheetData.headers.findIndex(
      (h) => String(h) === "(欠)預繳月數"
    );
    const amountColIdx = sheetData.headers.findIndex(
      (h) => String(h) === "(欠)預繳金額"
    );
    const residentIdColIdx = 1; // 住戶ID欄位

    if (monthsColIdx === -1 || amountColIdx === -1) return [];

    return sheetData.rows
      .filter((row) => {
        const months = parseFloat(String(row[monthsColIdx]));
        return !isNaN(months) && months <= -threshold;
      })
      .map((row) => ({
        id: String(row[residentIdColIdx]),
        amount: String(row[amountColIdx]),
        months: String(row[monthsColIdx]),
      }));
  };

  // Add button click handler
  const handleShowOverdue = (months: number) => {
    setOverdueMonthThreshold(months);
    setOverdueList(getOverdueAccounts(months));
    setOverdueDialogOpen(true);
  };

  const handleShowReminderLetter = (months: number) => {
    setOverdueMonthThreshold(months);
    setIsReminderLetterDialogOpen(true);
  };

  // Reusable dialog component
  function DataDialog({
    open,
    onOpenChange,
    title,
    items,
    itemLabel,
  }: {
    open: boolean;
    onOpenChange: (v: boolean) => void;
    title: string;
    items: any[];
    itemLabel?: (item: any) => ReactNode;
  }) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>
          <DialogDescription>
            {items && items.length > 0 ? (
              <ul className="max-h-96 overflow-y-auto list-disc pl-5">
                {items.map((item, idx) => (
                  <li key={idx} className="text-sm break-all">
                    {itemLabel ? itemLabel(item) : JSON.stringify(item)}
                  </li>
                ))}
              </ul>
            ) : (
              <div className="text-muted-foreground text-center py-4">
                無資料
              </div>
            )}
          </DialogDescription>
        </DialogContent>
      </Dialog>
    );
  }

  const loadSheetData = async () => {
    setIsFetchingSheet(true);
    setFetchSheetError(null);
    try {
      const result = await fetchManagementSheetData(targetYear);
      if (result.success && result.data && result.headers) {
        setSheetData({ headers: result.headers, rows: result.data });
        if (result.nameMap) {
          setNameMap(result.nameMap);
        }
        toast.success("Sheet data loaded successfully!");
        // Assume result.depositsUnmatched, result.depositsSmallAmount, result.withdrawals, result.withdrawalsUnmatched are returned
        if (result.depositsUnmatched) {
          setDepositsUnmatched(result.depositsUnmatched);
          saveToLocalStorage("depositsUnmatched", result.depositsUnmatched);
        }
        if (result.depositsSmallAmount) {
          setDepositsSmallAmount(result.depositsSmallAmount);
          saveToLocalStorage("depositsSmallAmount", result.depositsSmallAmount);
        }
        if (result.withdrawals) {
          setWithdrawals(result.withdrawals);
          saveToLocalStorage("withdrawals", result.withdrawals);
        }
        if (result.withdrawalsUnmatched) {
          setWithdrawalsUnmatched(result.withdrawalsUnmatched);
          saveToLocalStorage(
            "withdrawalsUnmatched",
            result.withdrawalsUnmatched
          );
        }
        if (result.motorBikeParkingLot) {
          setMotorBikeParkingLot(result.motorBikeParkingLot);
          saveToLocalStorage("motorBikeParkingLot", result.motorBikeParkingLot);
        }
        if (result.depositsSpecial) {
          setDepositsSpecial(result.depositsSpecial);
          saveToLocalStorage("depositsSpecial", result.depositsSpecial);
        }
      } else {
        setFetchSheetError(result.message);
        toast.error(`Failed to load sheet data: ${result.message}`);
        setSheetData(null);
      }
    } catch (error: any) {
      const msg = `Error fetching sheet: ${error.message || "Unknown error"}`;
      setFetchSheetError(msg);
      toast.error(msg);
      setSheetData(null);
    } finally {
      setIsFetchingSheet(false);
    }
  };

  useEffect(() => {
    loadSheetData();
  }, [targetYear]);

  const readFileAsText = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const text = e.target?.result as string;
        if (text) {
          resolve(text);
        } else {
          reject(new Error("Could not read file content."));
        }
      };
      reader.onerror = () => {
        reject(new Error("Failed to read the file."));
      };
      reader.readAsText(file);
    });
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (files.length === 0) {
      toast.error("Please select at least one CSV file to upload.");
      return;
    }

    const totalFiles = files.length;
    setUploadStatus({
      type: "loading",
      message: `Processing ${totalFiles} CSV file(s). This may take a moment...`,
    });
    toast.info(`Processing ${totalFiles} CSV file(s). This may take a moment...`);

    let successCount = 0;
    let errorCount = 0;
    let lastSuccessResult: any = {};

    for (let i = 0; i < totalFiles; i++) {
      const file = files[i];
      setUploadStatus(prev => ({
        ...prev,
        message: `Processing file ${i + 1} of ${totalFiles}: ${file.name}`
      }));

      try {
        const csvContent = await readFileAsText(file);
        const result = await uploadDepositToSheets(csvContent, enableUpdate.value);

        if (result.success) {
          successCount++;
          lastSuccessResult = result;
          toast.success(`Successfully processed ${file.name}.`);
          
          if (result.depositsMaintenanceFee && result.depositsMaintenanceFee.length > 0 && !enableUpdate.value) {
            setSheetData({
              headers: result.depositsMaintenanceFee[0],
              rows: result.depositsMaintenanceFee.slice(1),
            });
          } else {
            await loadSheetData();
          }
        } else {
          errorCount++;
          toast.error(`Error processing ${file.name}: ${result.message}`);
        }
      } catch (error: any) {
        errorCount++;
        toast.error(`Failed to process ${file.name}: ${error.message || "Unknown error"}`);
      }
    }

    const finalMessage = `Processing complete. ${successCount} succeeded, ${errorCount} failed.`;
    setUploadStatus({
      type: errorCount > 0 ? "error" : "success",
      message: finalMessage,
      spreadsheetUrl: lastSuccessResult.spreadsheetUrl,
    });
    toast.info(finalMessage);

    setFiles([]);
    const fileInput = document.getElementById("csv-file") as HTMLInputElement;
    if (fileInput) fileInput.value = "";
  };

  const handleAutoPayFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    setFileUploadStatus({ type: "idle", message: "" });
    if (event.target.files && event.target.files.length > 0) {
      // Validate file types
      const selectedFiles = Array.from(event.target.files);
      const validFiles = selectedFiles.filter(file => 
        file.type.startsWith('image/') || file.type === 'application/pdf'
      );

      if (validFiles.length < selectedFiles.length) {
        toast.error("Some files were not supported and have been ignored. Only images and PDF files are allowed.");
      }

      if (validFiles.length === 0) {
        toast.error("Please select valid image or PDF files.");
        event.target.value = '';
        return;
      }

      setAutoPayFiles(validFiles);
    } else {
      setAutoPayFiles([]);
    }
  };

  const handleImageUpload = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!autoPayFiles.length) {
      toast.error("請選擇至少一個檔案。");
      return;
    }
    setFileUploadStatus({ type: "loading", message: "Processing files..." });
    toast.info("Processing files. This may take a moment...");
    try {
      const results: any[] = [];
      let spreadsheetUrl = "";
      let googleDriveLink = "";
      for (const file of autoPayFiles) {
        const formData = new FormData();
        formData.append("file", file);
        const response = await fetch("/api/autopay-file-upload", {
          method: "POST",
          body: formData,
        });
        if (!response.ok) {
          results.push({
            success: false,
            message: `Server error: ${response.status}`,
          });
          continue;
        }
        const result = await response.json();
        if (result.success !== false && result.appendResult) {
          results.push(
            ...(Array.isArray(result.fillResultsArr)
              ? result.fillResultsArr
              : [result.fillResultsArr])
          );
          spreadsheetUrl = result.appendResult.spreadsheetUrl || spreadsheetUrl;
          googleDriveLink =
            result.appendResult.googleDriveLink || googleDriveLink;
        } else {
          results.push({
            success: false,
            message: result.message || "File upload failed",
          });
        }
      }
      const successful = results.filter((r: any) => r.success).length;
      const unsuccessful = results.filter((r: any) => !r.success);
      const unsuccessfulCount = unsuccessful.length;
      setFileUploadStatus({
        type: "success",
        message: `資料處理完成！成功填入：${successful} 筆，失敗：${unsuccessfulCount} 筆。`,
        spreadsheetUrl,
        googleDriveLink,
      });
      toast.success("Files processed and uploaded successfully!");
      setAutoPayFiles([]);
      const fileInput = document.getElementById(
        "autoPay-file"
      ) as HTMLInputElement;
      if (fileInput) fileInput.value = "";
      await loadSheetData();
    } catch (error: any) {
      const errorMessage = `File upload failed: ${error.message || "Unknown error"}`;
      setFileUploadStatus({ type: "error", message: errorMessage });
      toast.error(errorMessage);
    }
  };

  const renderStatusAlert = () => {
    if (
      uploadStatus.type === "idle" ||
      (uploadStatus.type === "loading" && !uploadStatus.message)
    )
      return null;

    let icon = <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
    let title = "Processing";
    let variant: "default" | "destructive" = "default";

    if (uploadStatus.type === "success") {
      icon = <CheckCircle className="h-5 w-5 text-green-500" />;
      title = "Success!";
    } else if (uploadStatus.type === "error") {
      icon = <XCircle className="h-5 w-5 text-red-500" />;
      title = "Error";
      variant = "destructive";
    } else if (uploadStatus.type === "loading") {
      title = "In Progress";
    }

    return (
      <Alert variant={variant} className="mt-6">
        <div className="flex items-center">
          {icon}
          <AlertTitle className="ml-2 font-semibold">{title}</AlertTitle>
        </div>
        <AlertDescription className="mt-2 ml-7">
          {uploadStatus.message}
          {uploadStatus.type === "success" && uploadStatus.spreadsheetUrl && (
            <a
              href={uploadStatus.spreadsheetUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="ml-2 inline-flex items-center text-sm font-medium text-blue-600 hover:underline"
            >
              View Uploaded Spreadsheet{" "}
              <ExternalLink className="ml-1 h-4 w-4" />
            </a>
          )}
        </AlertDescription>
      </Alert>
    );
  };

  const renderFileStatusAlert = () => {
    if (fileUploadStatus.type === "idle") return null;

    let icon = <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
    let title = "Processing File";
    let variant: "default" | "destructive" = "default";

    if (fileUploadStatus.type === "success") {
      icon = <CheckCircle className="h-5 w-5 text-green-500" />;
      title = "File Processed!";
    } else if (fileUploadStatus.type === "error") {
      icon = <XCircle className="h-5 w-5 text-red-500" />;
      title = "File Processing Error";
      variant = "destructive";
    }

    return (
      <Alert variant={variant} className="mt-6">
        <div className="flex items-center">
          {icon}
          <AlertTitle className="ml-2 font-semibold">{title}</AlertTitle>
        </div>
        <AlertDescription className="mt-2 ml-7">
          {fileUploadStatus.message}
          {fileUploadStatus.type === "success" &&
            fileUploadStatus.spreadsheetUrl && (
              <a
                href={fileUploadStatus.spreadsheetUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="ml-2 inline-flex items-center text-sm font-medium text-blue-600 hover:underline"
              >
                View Processed Data Spreadsheet{" "}
                <ExternalLink className="ml-1 h-4 w-4" />
              </a>
            )}
        </AlertDescription>
      </Alert>
    );
  };

  // 取得所有月份（header 格式為 MONTH_YEAR）
  // 取得所有月份（header 格式為 XXX_YYYY，例如 JAN_2025）
  const monthHeaders = useMemo(() => {
    if (!sheetData || !sheetData.headers) return [];
    // 篩選出格式為 "2025/01" 這類 header
    console.log("sheetData.headers: ", sheetData.headers);
    const monthHeaderRegex = /^\d{4}\/\d{1,2}$/; // Allows 1-12 or 01-12
    return sheetData.headers
      .filter((h) => typeof h === "string" && monthHeaderRegex.test(h))
      .map((h) => String(h));
  }, [sheetData]);

  // 取得指定月份未繳名單
  const getUnpaidList = (month: string) => {
    if (!sheetData) return [];
    const monthColIdx = sheetData.headers.findIndex((h) => String(h) === month);
    const residentIdColIdx = 1; // 住戶ID欄位
    if (monthColIdx === -1) return [];
    return sheetData.rows
      .filter(
        (row) => !row[monthColIdx] || String(row[monthColIdx]).trim() === ""
      )
      .map((row) => String(row[residentIdColIdx]));
  };

  const handleShowUnpaid = (month: string) => {
    setUnpaidList(getUnpaidList(month));
    setUnpaidMonth(month);
    setUnpaidDialogOpen(true);
  };

  // Dialog: 計算過濾後的未繳名單數量
  const filteredUnpaidList = useMemo(() => {
    return unpaidList.filter((id, _, arr) => {
      if (id === "08-01-1" && !arr.includes("06-01-1")) return false;
      return true;
    });
  }, [unpaidList]);

  // On mount, try to load from localStorage
  useEffect(() => {
    setDepositsUnmatched(loadFromLocalStorage("depositsUnmatched") || []);
    setDepositsSmallAmount(loadFromLocalStorage("depositsSmallAmount") || []);
    setDepositsSpecial(loadFromLocalStorage("depositsSpecial") || []);
    setWithdrawals(loadFromLocalStorage("withdrawals") || []);
    setWithdrawalsUnmatched(loadFromLocalStorage("withdrawalsUnmatched") || []);
    setMotorBikeParkingLot(loadFromLocalStorage("motorBikeParkingLot") || []);
  }, []);

  // Handler to open dialog
  const openDialog = (type: typeof dialogType) => {
    setDialogType(type);
    switch (type) {
      case "depositsUnmatched":
        setDialogData(depositsUnmatched);
        break;
      case "depositsSmallAmount":
        setDialogData(depositsSmallAmount);
        break;
      case "depositsSpecial":
        setDialogData(depositsSpecial);
        break;
      case "withdrawals":
        setDialogData(withdrawals);
        break;
      case "withdrawalsUnmatched":
        setDialogData(withdrawalsUnmatched);
        break;
      case "motorBikeParkingLot":
        setDialogData(motorBikeParkingLot);
        break;
      default:
        setDialogData([]);
    }
    setDialogOpen(true);
  };

  return (
    <div className="container mx-auto p-2 space-y-4">
      {/* Collapsible Upload Area */}
      <div className="w-full max-w-4xl mx-auto flex flex-col md:flex-row gap-8">
        {/* Deposit CSV Upload (left) */}
        <div className="w-full md:w-1/2">
          <div className="w-full max-w-lg mx-auto">
            <button
              type="button"
              className="flex items-center w-full px-4 py-2 mb-2 bg-muted rounded hover:bg-muted/80 transition-colors border border-border"
              onClick={() => setIsUploadOpen((open) => !open)}
            >
              {isUploadOpen ? (
                <ChevronDown className="mr-2 h-5 w-5" />
              ) : (
                <ChevronRight className="mr-2 h-5 w-5" />
              )}
              <span className="font-semibold">上傳銀行匯出CSV檔</span>
            </button>
            {isUploadOpen && (
              <Card className="w-full shadow-lg">
                <CardHeader className="text-center">
                  <div className="flex flex-row justify-center items-center space-x-4">
                    <CardTitle className="text-2xl">
                      上傳銀行匯出CSV檔
                    </CardTitle>
                    <Switch
                      checked={enableUpdate.value}
                      onCheckedChange={enableUpdate.onChange}
                    />
                  </div>
                  <CardDescription>
                    Select your CSV file to process and upload it to Google
                    Sheets.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                      <Label
                        htmlFor="csv-file"
                        className="text-sm font-medium sr-only"
                      >
                        Select CSV File
                      </Label>
                      <div
                        className={`flex items-center justify-center w-full p-2 border-2 border-dashed rounded-md cursor-pointer 
                        ${uploadStatus.type === "loading" ? "border-gray-300 bg-gray-50" : "border-gray-300 hover:border-primary"}`}
                        onClick={() =>
                          document.getElementById("csv-file")?.click()
                        }
                      >
                        <Input
                          id="csv-file"
                          type="file"
                          accept=".csv"
                          multiple
                          onChange={handleFileChange}
                          className="hidden"
                          disabled={uploadStatus.type === "loading"}
                        />
                        {files.length === 0 && (
                          <div className="text-center py-4">
                            <FileText className="mx-auto h-10 w-10 text-gray-400 mb-2" />
                            <p className="text-sm text-gray-600">
                              <span className="font-semibold text-primary">
                                Click to upload
                              </span>{" "} or drag and drop
                            </p>
                            <p className="text-xs text-gray-500">
                              CSV files only (可多選)
                            </p>
                          </div>
                        )}
                        {files.length > 0 && (
                          <div className="text-center py-4">
                            <CheckCircle className="mx-auto h-10 w-10 text-green-500 mb-2" />
                            <ul className="text-sm font-medium text-gray-700 list-none max-h-24 overflow-y-auto">
                              {files.map((f, i) => (
                                <li key={i}>{f.name} ({(f.size / 1024).toFixed(2)} KB)</li>
                              ))}
                            </ul>
                            <p className="text-xs text-gray-500 mt-2">
                              點擊可重新選擇
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                    {renderStatusAlert()}
                    <Button
                      type="submit"
                      className="w-full mt-6"
                      disabled={uploadStatus.type === "loading" || files.length === 0}
                    >
                      {uploadStatus.type === "loading" ? (
                        <>
                          {" "}
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />{" "}
                          Processing...
                        </>
                      ) : (
                        "Upload and Process"
                      )}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
        {/* File Upload (right) */}
        <div className="w-full md:w-1/2">
          <div className="w-full max-w-lg mx-auto">
            <button
              type="button"
              className="flex items-center w-full px-4 py-2 mb-2 bg-muted rounded hover:bg-muted/80 transition-colors border border-border"
              onClick={() => setIsUploadOpen((open) => !open)}
            >
              {isUploadOpen ? (
                <ChevronDown className="mr-2 h-5 w-5" />
              ) : (
                <ChevronRight className="mr-2 h-5 w-5" />
              )}
              <span className="font-semibold">上傳管理費自動轉帳文件</span>
            </button>
            {isUploadOpen && (
              <Card className="w-full shadow-lg">
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">
                    上傳管理費自動轉帳文件
                  </CardTitle>
                  <CardDescription>
                    上傳管理費自動轉帳對帳單圖片或PDF檔案，AI 辨識後自動填入 Google
                    Sheets。
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleImageUpload} className="space-y-6">
                    <div>
                      <Label
                        htmlFor="autoPay-file"
                        className="text-sm font-medium sr-only"
                      >
                        Select Image or PDF File
                      </Label>
                      <div
                        className={`flex items-center justify-center w-full p-2 border-2 border-dashed rounded-md cursor-pointer ${fileUploadStatus.type === "loading"
                            ? "border-gray-300 bg-gray-50"
                            : "border-gray-300 hover:border-primary"}`}
                        onClick={() =>
                          document.getElementById("autoPay-file")?.click()
                        }
                      >
                        <Input
                          id="autoPay-file"
                          type="file"
                          accept="image/*,.pdf"
                          multiple
                          onChange={handleAutoPayFileChange}
                          className="hidden"
                          disabled={fileUploadStatus.type === "loading"}
                        />
                        {!autoPayFiles.length && (
                          <div className="text-center py-4">
                            <FileText className="mx-auto h-10 w-10 text-gray-400 mb-2" />
                            <p className="text-sm text-gray-600">
                              <span className="font-semibold text-primary">
                                Click to upload
                              </span>{" "} or drag and drop
                            </p>
                            <p className="text-xs text-gray-500">
                              Images and PDF files (可多選)
                            </p>
                          </div>
                        )}
                        {!!autoPayFiles.length && (
                          <div className="text-center py-4">
                            <CheckCircle className="mx-auto h-10 w-10 text-green-500 mb-2" />
                            <ul className="text-sm font-medium text-gray-700">
                              {autoPayFiles.map((file, idx) => (
                                <li key={idx}>
                                  {file.name}{" "}
                                  <span className="text-xs text-gray-500">
                                    ({(file.size / 1024).toFixed(2)} KB)
                                  </span>
                                </li>
                              ))}
                            </ul>
                            <p className="text-xs text-gray-500">
                              點擊可重新選擇
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                    {/* Status Alert for File Upload */}
                    {renderFileStatusAlert()}
                    <Button
                      type="submit"
                      className="w-full mt-6"
                      disabled={
                        fileUploadStatus.type === "loading" ||
                        !autoPayFiles.length
                      }
                    >
                      {fileUploadStatus.type === "loading" ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />{" "}
                          Processing...
                        </>
                      ) : (
                        "Upload and Recognize"
                      )}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
      <Card className="w-full shadow-lg">
        <CardHeader className="pb-2">
          <div className="flex flex-col justify-between items-start">
            <div className="flex items-center space-x-2">
              <TableIcon className="h-6 w-6 text-primary" />
              <CardTitle>社區管理費繳費統計表</CardTitle>
              {/* Year Picker  */}
              <div className="flex items-center space-x-2 pl-8">
                <Label htmlFor="target-year" className="text-sm font-medium">
                  選擇年份
                </Label>
                <Select value={targetYear} onValueChange={setTargetYear}>
                  <SelectTrigger className="w-24 h-8 text-sm">
                    <SelectValue placeholder="年份" />
                  </SelectTrigger>
                  <SelectContent>
                    {yearOptions.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={loadSheetData}
                disabled={isFetchingSheet}
              >
                {isFetchingSheet ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="mr-2 h-4 w-4" />
                )}
                更新資料
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={!sheetData || sheetData.rows.length === 0 || !isFontLoaded || isFontLoading}
                    className="ml-2"
                  >
                    下載報表 <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={handleDownloadCSV}>
                    <FileText className="mr-2 h-4 w-4" />
                    下載CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleDownloadPDF}>
                    <FileText className="mr-2 h-4 w-4" /> {/* Consider a PDF specific icon if available */}
                    下載PDF
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              {/* New: Buttons for each data type */}
              <div className="flex space-x-1 pl-8">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => openDialog("depositsUnmatched")}
                >
                  未歸類存入
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => openDialog("depositsSmallAmount")}
                >
                  小額存入
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => openDialog("depositsSpecial")}
                >
                  特殊存入
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => openDialog("withdrawals")}
                >
                  已歸類提領
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => openDialog("withdrawalsUnmatched")}
                >
                  未歸類提領
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => openDialog("motorBikeParkingLot")}
                >
                  機踏車停車費
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="sm" variant="secondary">
                      查詢欠費住戶 <ChevronDown className="ml-2 h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    {Array.from({ length: 12 }, (_, i) => i + 1).map(
                      (month) => (
                        <DropdownMenuItem
                          key={month}
                          onClick={() => handleShowOverdue(month)}
                        >
                          欠費 &gt;= {month} 個月
                        </DropdownMenuItem>
                      )
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="sm" variant="secondary">
                      催繳通知函 <ChevronDown className="ml-2 h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    {Array.from({ length: 12 }, (_, i) => i + 1).map(
                      (month) => (
                        <DropdownMenuItem
                          key={month}
                          onClick={() => handleShowReminderLetter(month)}
                        >
                          欠費 &gt;= {month} 個月
                        </DropdownMenuItem>
                      )
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="overflow-auto p-0">
          {isFetchingSheet && !sheetData && (
            <div className="flex justify-center items-center py-4">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="ml-3 text-muted-foreground">
                Loading sheet data...
              </p>
            </div>
          )}
          {fetchSheetError && !isFetchingSheet && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertTitle>Error Loading Data</AlertTitle>
              <AlertDescription>{fetchSheetError}</AlertDescription>
            </Alert>
          )}
          {!isFetchingSheet && sheetData && sheetData.rows.length > 0 && (
            <>
              <div className="max-w-[92vw] flex items-center justify-start gap-x-2 mb-2">
                <div className="flex items-center font-bold ml-[1.3rem] p-2 whitespace-nowrap">
                  ＜ 未繳費名單 ＞
                </div>
                {/* 動態產生每月未繳名單按鈕（不顯示住戶ID欄） */}
                {monthHeaders.map((month) => (
                  <Button
                    key={month}
                    className="h-8 text-[14px] px-1.5 font-sans"
                    variant="secondary"
                    onClick={() => handleShowUnpaid(month)}
                  >
                    {month}
                  </Button>
                ))}
              </div>
              <div className="overflow-x-auto max-h-[500px] xl:max-h-[720px]">
                <Table>
                  <TableHeader className="sticky top-0 bg-background z-10">
                    <TableRow>
                      {sheetData.headers.map((header, index) => (
                        // 不顯示 住戶編號 欄的尚未繳費名單按鈕
                        <TableHead
                          key={index}
                          className="px-[0.4rem] font-bold text-right whitespace-nowrap"
                        >
                          {String(header)}{" "} 
                          {/* Ensure header is a string for rendering */}
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sheetData.rows.map((row, rowIndex) => (
                      <TableRow key={rowIndex}>
                        {row.map((cell, cellIndex) => (
                          <TableCell
                            key={cellIndex}
                            className="py-1 font-semibold text-right whitespace-nowrap"
                          >
                            {String(cell)}{" "} 
                            {/* Ensure cell is a string for rendering */}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </>
          )}
          {!isFetchingSheet && sheetData && sheetData.rows.length === 0 && (
            <p className="text-center text-muted-foreground py-10">
              No data found in the sheet.
            </p>
          )}
        </CardContent>
      </Card>

      {/* 尚未繳費名單 Dialog */}
      {unpaidDialogOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-green-200 dark:bg-green-700 bg-opacity-90 rounded-lg shadow-lg max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-bold">
                {unpaidMonth} 尚未繳費名單 （共 {filteredUnpaidList.length} 筆）
              </h2>
              <button
                onClick={() => setUnpaidDialogOpen(false)}
                className="text-gray-500 dark:text-gray-200 hover:text-gray-700 text-xl"
              >
                ×
              </button>
            </div>
            <div className="flex items-center justify-between mb-2 gap-x-2">
              <p className="w-60 text-sm text-gray-500 dark:text-gray-300">
                註：7-ELEVEN包含「06-01-1」、「08-01-1」，繳費金額歸戶於「06-01-1」。
              </p>
              <Button
                size="sm"
                variant="outline"
                onClick={async () => {
                  if (filteredUnpaidList.length === 0) return;
                  try {
                    await navigator.clipboard.writeText(
                      filteredUnpaidList.join("\n")
                    );
                    toast.success("已複製未繳住戶ID清單！");
                  } catch {
                    toast.error("複製失敗，請手動選取複製。");
                  }
                }}
              >
                複製清單
              </Button>
            </div>
            {filteredUnpaidList.length === 0 ? (
              <p className="text-center text-muted-foreground">
                全部住戶皆已繳費！
              </p>
            ) : (
              <ul className="max-h-80 overflow-y-auto list-disc pl-5">
                {filteredUnpaidList.map((id, idx) => (
                  <li
                    key={id + idx}
                    className="lining-nums font-mono font-stretch-ultra-expanded text-sm w-40 inline-block text-left"
                  >
                    {id}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      )}
      {/* Data Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {dialogType === "depositsUnmatched"
                ? "未歸類存入"
                : dialogType === "depositsSmallAmount"
                  ? "小額存入"
                  : dialogType === "depositsSpecial"
                    ? "特殊存入"
                    : dialogType === "withdrawals"
                      ? "已歸類提領"
                      : dialogType === "withdrawalsUnmatched"
                        ? "未歸類提領"
                        : dialogType === "motorBikeParkingLot"
                          ? "機踏車停車費"
                          : ""}
            </DialogTitle>
          </DialogHeader>
          <div className="flex justify-between items-center">
            <Button
              size="sm"
              variant="outline"
              onClick={async () => {
                // Prepare tab-separated or comma-separated values for copy
                const header = [
                  "交易日期",
                  "類型",
                  "金額",
                  "說明",
                  "交易資訊",
                  "備註",
                ];
                const rows = dialogData.map((item) => {
                  const row = item?.row || item || {};
                  return [
                    row["交易日期"] || item.transactionDate || "",
                    row["說明"] || item.description || row["說明"] || "",
                    row["類型"] || item.type || "",
                    row["金額"] || item.amount || "",
                    row["交易資訊"] || item.info || row["交易資訊"] || "",
                    row["備註"] || item.remark || row["備註"] || "",
                  ].map((v) => (typeof v === "undefined" ? "" : String(v)));
                });
                const text = [header, ...rows]
                  .map((r) => r.join("\t"))
                  .join("\n");
                try {
                  await navigator.clipboard.writeText(text);
                  toast.success("已複製資料！");
                } catch {
                  toast.error("複製失敗，請手動選取複製。");
                }
              }}
            >
              複製資料
            </Button>
          </div>
          <div className="overflow-x-auto max-h-[60vh]">
            <table className="min-w-full border text-xs rounded-lg shadow-lg">
              <thead>
                <tr className="bg-muted">
                  <th className="px-2 py-1 border whitespace-nowrap">
                    交易日期
                  </th>
                  <th className="px-2 py-1 border whitespace-nowrap">說明</th>
                  <th className="px-2 py-1 border whitespace-nowrap">類型</th>
                  <th className="px-2 py-1 border text-right whitespace-nowrap">
                    金額
                  </th>
                  <th className="px-2 py-1 border whitespace-nowrap">
                    交易資訊
                  </th>
                  <th className="px-2 py-1 border whitespace-nowrap">備註</th>
                </tr>
              </thead>
              <tbody>
                {dialogData && dialogData.length > 0 ? (
                  dialogData.map((item, idx) => {
                    const row = item?.row || item || {};
                    return (
                      <tr
                        key={idx}
                        className="even:bg-gray-50 dark:even:bg-gray-800"
                      >
                        <td className="px-2 py-1 border whitespace-nowrap">
                          {row["交易日期"] || item.transactionDate || ""}
                        </td>
                        <td className="px-2 py-1 border whitespace-nowrap">
                          {row["說明"] || item.description || row["說明"] || ""}
                        </td>
                        <td className="px-2 py-1 border">
                          {row["類型"] || item.type || ""}
                        </td>
                        <td className="px-2 py-1 border text-right whitespace-nowrap">
                          {row["金額"] || item.amount || ""}
                        </td>
                        <td className="px-2 py-1 border whitespace-nowrap">
                          {row["交易資訊"] ||
                            item.info ||
                            row["交易資訊"] ||
                            ""}
                        </td>
                        <td className="px-2 py-1 border whitespace-nowrap">
                          {row["備註"] || item.remark || row["備註"] || ""}
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td
                      colSpan={6}
                      className="text-center py-4 text-muted-foreground"
                    >
                      無資料
                    </td>
                  </tr>
                )}
              </tbody>
              {/* Totals row */}
              {dialogData && dialogData.length > 0 && (
                <tfoot>
                  <tr className="bg-muted font-bold">
                    <td className="px-2 py-1 border text-right" colSpan={3}>
                      合計
                    </td>
                    <td className="px-2 py-1 border text-right whitespace-nowrap">
                      {dialogData
                        .reduce((sum, item) => {
                          const row = item?.row || item || {};
                          // Try both "金額" and amount, and parse as float
                          {console.log("row", row);
                          console.log("item", item);}
                          const val = row["金額"] ?? item.amount ?? "";
                          const num =
                            typeof val === "string"
                              ? parseFloat(val.replace(/,/g, ""))
                              : typeof val === "number"
                                ? val
                                : 0;
                          return sum + (isNaN(num) ? 0 : num);
                        }, 0)
                        .toLocaleString("en-US", {
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 2,
                        })}
                    </td>
                    <td className="px-2 py-1 border" colSpan={2}></td>
                  </tr>
                </tfoot>
              )}
            </table>
          </div>
        </DialogContent>
      </Dialog>
      {overdueDialogOpen && (
        <Dialog open={overdueDialogOpen} onOpenChange={setOverdueDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                欠費 {overdueMonthThreshold} 個月以上住戶
              </DialogTitle>
            </DialogHeader>
            <div className="flex justify-between items-center mb-4">
              <div className="text-sm text-muted-foreground">
                共 {overdueList.length} 筆
              </div>
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={async () => {
                    const header = ["序號", "住戶編號", "累計未繳金額", "期數"];
                    const rows = overdueList.map((item, idx) => [
                      idx + 1,
                      item.id,
                      item.amount,
                      item.months,
                    ]);
                    const text = [header, ...rows]
                      .map((r) => r.join("\t"))
                      .join("\n");

                    try {
                      await navigator.clipboard.writeText(text);
                      toast.success("已複製資料！");
                    } catch {
                      toast.error("複製失敗，請手動選取複製。");
                    }
                  }}
                >
                  複製資料
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                  // Create a new window for printing
                  const printWindow = window.open('', '_blank');
                  if (printWindow) {
                    printWindow.document.write(`
                    <html>
                      <head>
                      <title>欠費 ${overdueMonthThreshold} 個月以上住戶</title>
                      <style>
                      body { font-family: Arial, sans-serif; margin: 20px; }
                      table { 
                        border-collapse: collapse; 
                        width: 100%; 
                        font-size: 1.125rem; /* 18px - equivalent to text-lg */
                      }
                      th, td { 
                        border: 1px solid #070707ff; 
                        padding: 4px; 
                        text-align: center;
                        font-size: 1.125rem; /* 18px */
                      }
                      th { background-color: #f2f2f2; }
                      .pt-2 { padding-top: 10px; }
                      .py-2 { padding-top: 10px; padding-bottom: 10px; }
                      .text-right { text-align: right; }
                      .text-center { text-align: center; }
                      .text-xl { 
                        font-size: 1.25rem; /* 20px */
                        line-height: 1.75rem; /* 28px */
                      }
                      .text-lg { 
                        font-size: 1.125rem; /* 18px */
                        line-height: 1.75rem; /* 28px */
                      }
                      .text-sm { 
                        font-size: 0.875rem; /* 14px */
                        line-height: 1.25rem; /* 20px */
                      }
                      .text-red { color: red; }
                      .font-bold { font-weight: bold; }
                      .underline { text-decoration: underline; }
                      .bg-muted { background-color: #f8f9fa; }
                      h1 { margin-bottom: 20px; }
                      .summary { margin-bottom: 10px; }
                      /* Add fixed width for 序號 column */
                      th:first-child, td:first-child { 
                      width: 3rem;
                      min-width: 3rem;
                      max-width: 3rem;
                      }
                      .tracking-wider { letter-spacing: 0.2em; }
                      </style>
                      </head>
                      <body>
                      <h1 class="text-center underline tracking-wider">繳 費 提 醒</h1>
                      <div class="pt-2 text-xl font-bold">
                      截至 ${new Date().getFullYear()} 年 ${new Date().getMonth()} 月 ${new Date(new Date().getFullYear(), new Date().getMonth(), 0).getDate()} 日止，社區管理費逾期未繳名單如下，惠請住戶儘快繳納。
                      </div>
                      <div class="text-xl text-red font-bold">※如有筆誤，請攜帶繳費收據更正。</div>
                      <div class="py-2 text-center text-xl font-bold tracking-wider">【管理費逾期未繳納名單】</div>
                      <table>
                      <thead>
                      <tr class="bg-muted">
                      <th>序號</th>
                      <th>住戶編號</th>
                      <th>累計未繳金額</th>
                      <th>期數</th>
                      </tr>
                      </thead>
                      <tbody>
                      ${overdueList.map((item, idx) => `
                      <tr>
                      <td class="text-center">${idx + 1}</td>
                      <td class="text-center">${item.id}</td>
                      <td class="text-right">${parseFloat(item.amount).toLocaleString()}</td>
                      <td class="text-right">${parseFloat(item.months)}</td>
                      </tr>
                      `).join('')}
                      </tbody>
                      <tfoot>
                      <tr class="bg-muted font-bold">
                      <td class="text-center"></td>
                      <td class="text-center">合計</td>
                      <td class="text-right">${totalOverdueAmount.toLocaleString()}</td>
                      <td class="text-right">${totalOverdueMonths}</td>
                      </tr>
                      </tfoot>
                      </table>
                      <div class="pt-2 text-center text-xl font-bold">僑星福華社區管理委員會 敬啟</div>
                      <div class="text-center text-xl font-bold">
                      中華民國 ${new Date().getFullYear() - 1911} 年 ${new Date().getMonth() + 1} 月 ${new Date().getDate()} 日
                      </div>
                      </body>
                    </html>
                    `);
                    printWindow.document.close();
                    printWindow.print();
                  }
                  }}
                >
                  列印
                </Button>
              </div>
            </div>
            <div className="overflow-x-auto max-h-[60vh]">
              <div className="py-2 text-center text-lg font-bold tracking-widest">【管理費逾期未繳納名單】</div>
                <table className="min-w-full border rounded-lg shadow-lg">
                  <thead className="text-sm">
                    <tr className="bg-muted">
                      <th className="w-10 px-1 py-1 border">序號</th>
                      <th className="px-2 py-1 border">住戶編號</th>
                      <th className="px-2 py-1 border">累計未繳金額</th>
                      <th className="px-2 py-1 border">期數</th>
                    </tr>
                  </thead>
                  <tbody className="text-sm">
                    {overdueList.map((item, idx) => (
                      <tr
                        key={idx}
                        className="even:bg-gray-50 dark:even:bg-gray-800"
                      >
                        <td className="px-1 py-1 border text-center">
                          {idx + 1}
                        </td>
                        <td className="px-4 py-1 border text-center">
                          {item.id}
                        </td>
                        <td className="px-4 py-1 border text-right">
                          {item.amount.toLocaleString()}
                        </td>
                        <td className="px-4 py-1 border text-right">
                          {parseFloat(item.months)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="bg-muted font-bold text-sm">
                      <td className="text-center"></td>
                      <td className="px-4 py-1 border text-center">合計</td>
                      <td className="px-4 py-1 border text-right">
                        {totalOverdueAmount.toLocaleString()}
                      </td>
                      <td className="px-4 py-1 border text-right">
                        {totalOverdueMonths}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              <div className="pt-4 text-center text-sm font-bold">僑星福華社區管理委員會 敬啟</div>
              <div className="text-center text-sm font-bold">
                中華民國 {new Date().getFullYear() - 1911} 年 {new Date().getMonth() + 1} 月 {new Date().getDate()} 日
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
      {isReminderLetterDialogOpen && (
        <ReminderLetterDialog
          sheetData={reminderSheetData}
          overdueMonthThreshold={overdueMonthThreshold}
          open={isReminderLetterDialogOpen}
          onOpenChange={setIsReminderLetterDialogOpen}
        />
      )}
    </div>
  );
}