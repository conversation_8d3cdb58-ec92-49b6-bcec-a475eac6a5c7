import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET() {
  try {
    const paymentRequests = await prisma.paymentRequest.findMany({
      include: {
        paymentAccounts: {
          where: { isActive: true },
          orderBy: [
            { isDefault: 'desc' },
            { accountType: 'asc' },
            { createdAt: 'asc' }
          ]
        },
        payments: {
          include: {
            remitterAccount: true,
            payeeAccount: true
          },
          orderBy: { sequenceId: 'asc' }
        }
      },
      orderBy: [
        { year: 'desc' },
        { month: 'desc' },
        { createdAt: 'desc' }
      ]
    })
    
    return NextResponse.json(paymentRequests)
  } catch (error) {
    console.error('Error fetching payment requests:', error)
    return NextResponse.json(
      { error: 'Failed to fetch payment requests' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name, year, month } = await request.json()
    
    const paymentRequest = await prisma.paymentRequest.create({
      data: { name, year, month },
      include: {
        payments: true,
        paymentAccounts: true
      }
    })
    
    return NextResponse.json(paymentRequest, { status: 201 })
  } catch (error: any) {
    console.error('Error creating payment request:', error)
    
    if (error.code === 'P2002') {
      return NextResponse.json(
        { error: 'Payment request for this year and month already exists' },
        { status: 409 }
      )
    } else {
      return NextResponse.json(
        { error: 'Failed to create payment request' },
        { status: 500 }
      )
    }
  }
}
