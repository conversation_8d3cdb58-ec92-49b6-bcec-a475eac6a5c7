import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const {
      accountType,
      bankCode,
      bankName,
      branchName,
      accountNumber,
      accountName,
      isDefault,
      paymentRequestId
    } = await request.json()

    // If this is being set as default, unset other defaults of the same type
    if (isDefault) {
      await prisma.paymentAccount.updateMany({
        where: {
          paymentRequestId,
          accountType,
          isDefault: true
        },
        data: { isDefault: false }
      })
    }

    const paymentAccount = await prisma.paymentAccount.create({
      data: {
        accountType,
        bankCode,
        bankName,
        branchName,
        accountNumber,
        accountName,
        isDefault,
        paymentRequestId
      }
    })

    return NextResponse.json(paymentAccount, { status: 201 })
  } catch (error) {
    console.error('Error creating bank account:', error)
    return NextResponse.json(
      { error: 'Failed to create bank account' },
      { status: 500 }
    )
  }
}