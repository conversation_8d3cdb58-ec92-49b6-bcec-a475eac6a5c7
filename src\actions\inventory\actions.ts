'use server';

import { ItemType } from '../../../prisma/app/generated/prisma/client';
import { SaleFormData, InventoryData } from '@/types/inventory';
import { auth } from '@clerk/nextjs/server';
import { prisma } from "@/lib/db";

export async function isSuperUser() {
  const { userId } = await auth();
  return userId === process.env.YOUNKER_TW_USER_ID;
}

// Get all inventory items
export async function getInventory(): Promise<InventoryData[]> {
  const inventory = await prisma.inventory.findMany({
    select: {
      itemType: true,
      current: true,
      price: true
    },
    orderBy: {
      itemType: 'asc'
    }
  });
  
  return inventory;
}

// Get inventory for a specific item
export async function getInventoryByType(itemType: ItemType) {
  return await prisma.inventory.findUnique({
    where: { itemType }
  });
}

// Process a sale transaction
export async function processSale(saleData: SaleFormData) {
  const { itemType, quantity, unitPrice, residentName, address, receiptNo, recipient, date } = saleData;
  
  return await prisma.$transaction(async (tx) => {
    // Check current inventory
    const inventory = await tx.inventory.findUnique({
      where: { itemType }
    });
    
    if (!inventory || inventory.current < quantity) {
      throw new Error(`Insufficient stock for ${itemType}`);
    }
    
    // Find or create resident
    const resident = await tx.resident.upsert({
      where: {
        name_address: {
          name: residentName,
          address: address
        }
      },
      update: {},
      create: {
        name: residentName,
        address: address
      }
    });
    
    // Create sale record
    const sale = await tx.sale.create({
      data: {
        date: new Date(date),
        receiptNo: receiptNo || undefined,
        recipient: recipient || undefined,
        residentId: resident.id,
        residentName: residentName,
        address: address,
        totalAmount: quantity * unitPrice
      }
    });
    
    // Create sale item
    await tx.saleItem.create({
      data: {
        saleId: sale.id,
        inventoryId: inventory.id,
        itemType: itemType,
        quantity: quantity,
        unitPrice: unitPrice,
        lineTotal: quantity * unitPrice
      }
    });
    
    // Update inventory
    const newStock = inventory.current - quantity;
    await tx.inventory.update({
      where: { itemType },
      data: { current: newStock }
    });
    
    // Record stock movement
    await tx.stockMovement.create({
      data: {
        inventoryId: inventory.id,
        type: 'SALE',
        quantity: -quantity,
        reason: `Sale to ${residentName}`,
        reference: sale.id,
        previousStock: inventory.current,
        newStock: newStock
      }
    });
    
    return sale;
  });
}

// Update inventory stock (for restocking)
export async function updateInventoryStock(itemType: ItemType, quantityChange: number, reason: string = 'Manual adjustment') {
  return await prisma.$transaction(async (tx) => {
    const inventory = await tx.inventory.findUnique({
      where: { itemType }
    });
    
    if (!inventory) {
      throw new Error(`Inventory not found for ${itemType}`);
    }
    
    const newStock = Math.max(0, inventory.current + quantityChange);
    
    // Update inventory
    await tx.inventory.update({
      where: { itemType },
      data: { current: newStock }
    });
    
    // Record stock movement
    await tx.stockMovement.create({
      data: {
        inventoryId: inventory.id,
        type: quantityChange > 0 ? 'PURCHASE' : 'ADJUSTMENT',
        quantity: quantityChange,
        reason: reason,
        previousStock: inventory.current,
        newStock: newStock
      }
    });
    
    return { previousStock: inventory.current, newStock };
  });
}

// Get sales history
export async function getSalesHistory(limit: number = 100) {
  return await prisma.sale.findMany({
    include: {
      saleItems: {
        include: {
          inventory: true
        }
      },
      resident: true
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: limit
  });
}

// Delete a sale
export async function deleteSale(saleId: string) {
  const { userId } = await auth();
  if (userId !== process.env.YOUNKER_TW_USER_ID) {
    throw new Error('Unauthorized action.');
  }

  return await prisma.$transaction(async (tx) => {
    const sale = await tx.sale.findUnique({
      where: { id: saleId },
      include: { saleItems: true },
    });

    if (!sale) {
      throw new Error('Sale not found');
    }

    for (const item of sale.saleItems) {
      const inventory = await tx.inventory.findUnique({
        where: { id: item.inventoryId },
      });

      if (inventory) {
        const newStock = inventory.current + item.quantity;
        await tx.inventory.update({
          where: { id: item.inventoryId },
          data: { current: newStock },
        });

        await tx.stockMovement.create({
          data: {
            inventoryId: item.inventoryId,
            type: 'ADJUSTMENT',
            quantity: item.quantity,
            reason: `Return from deleted sale ${sale.id}`,
            reference: sale.id,
            previousStock: inventory.current,
            newStock: newStock,
          },
        });
      }
    }

    await tx.saleItem.deleteMany({ where: { saleId: sale.id } });
    const deletedSale = await tx.sale.delete({ where: { id: saleId } });

    return deletedSale;
  });
}

// Update a sale
export async function updateSale(saleId: string, newData: SaleFormData) {
    const { userId } = await auth();
    if (userId !== process.env.YOUNKER_TW_USER_ID) {
        throw new Error('Unauthorized action.');
    }

    return await prisma.$transaction(async (tx) => {
        const sale = await tx.sale.findUnique({
            where: { id: saleId },
            include: { saleItems: true }
        });

        if (!sale) throw new Error("Sale not found");
        const originalItem = sale.saleItems[0];
        if (!originalItem) throw new Error("Sale item not found");

        const newInventoryInfo = await tx.inventory.findUnique({ where: { itemType: newData.itemType }});
        if (!newInventoryInfo) throw new Error("New inventory item type not found.");

        // If item type is the same
        if (originalItem.itemType === newData.itemType) {
            const quantityDiff = newData.quantity - originalItem.quantity;
            if (quantityDiff !== 0) {
                const inventory = await tx.inventory.findUnique({ where: { itemType: newData.itemType }});
                if (!inventory) throw new Error("Inventory not found");
                const newStock = inventory.current - quantityDiff;
                if (newStock < 0) throw new Error("Insufficient stock");
                await tx.inventory.update({
                    where: { itemType: newData.itemType },
                    data: { current: newStock }
                });
            }
        } else { // If item type is different
            // Return original item to stock
            const originalInventory = await tx.inventory.findUnique({ where: { itemType: originalItem.itemType }});
            if (!originalInventory) throw new Error("Original inventory not found");
            await tx.inventory.update({
                where: { itemType: originalItem.itemType },
                data: { current: originalInventory.current + originalItem.quantity }
            });

            // Deduct new item from stock
            if (newInventoryInfo.current < newData.quantity) throw new Error("Insufficient stock for new item");
            await tx.inventory.update({
                where: { itemType: newData.itemType },
                data: { current: newInventoryInfo.current - newData.quantity }
            });
        }

        // Update sale and saleItem
        await tx.sale.update({
            where: { id: saleId },
            data: {
                date: new Date(newData.date),
                receiptNo: newData.receiptNo,
                recipient: newData.recipient,
                residentName: newData.residentName,
                address: newData.address,
                totalAmount: newData.quantity * newData.unitPrice
            }
        });

        await tx.saleItem.update({
            where: { id: originalItem.id },
            data: {
                inventoryId: newInventoryInfo.id,
                itemType: newData.itemType,
                quantity: newData.quantity,
                unitPrice: newData.unitPrice,
                lineTotal: newData.quantity * newData.unitPrice
            }
        });

        return sale;
    });
}

// Get sales by date range
export async function getSalesByDateRange(startDate: Date, endDate: Date) {
  return await prisma.sale.findMany({
    where: {
      date: {
        gte: startDate,
        lte: endDate
      }
    },
    include: {
      saleItems: {
        include: {
          inventory: true
        }
      }
    },
    orderBy: {
      date: 'desc'
    }
  });
}

// Get low stock alerts
export async function getLowStockAlerts() {
  return await prisma.inventory.findMany({
    where: {
      current: {
        lte: prisma.inventory.fields.minStock
      }
    }
  });
}

// Get inventory movements history
export async function getStockMovements(itemType?: ItemType, limit: number = 50) {
  const where = itemType ? {
    inventory: {
      itemType: itemType
    }
  } : {};
  
  return await prisma.stockMovement.findMany({
    where,
    include: {
      inventory: true
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: limit
  });
}

// Sales statistics
export async function getSalesStats(startDate?: Date, endDate?: Date) {
  const where = startDate && endDate ? {
    date: {
      gte: startDate,
      lte: endDate
    }
  } : {};
  
  const totalSales = await prisma.sale.aggregate({
    where,
    _sum: {
      totalAmount: true
    },
    _count: {
      id: true
    }
  });
  
  const salesByItem = await prisma.saleItem.groupBy({
    by: ['itemType'],
    where: {
      sale: where
    },
    _sum: {
      quantity: true,
      lineTotal: true
    }
  });
  
  return {
    totalAmount: totalSales._sum.totalAmount || 0,
    totalTransactions: totalSales._count.id,
    salesByItem
  };
}