'use server';

import { google } from "googleapis";
import { authenticateGoogleOAuth2 } from "@/lib/google/authenticate";
import type { AppendResult, AutoPayResidentData } from './append-autopay-resident-to-sheet';

async function updateResidentAmount({
  residentId,
  monthYear,
  amountDelta,
}: {
  residentId: string;
  monthYear: string;
  amountDelta: number;
}): Promise<{ success: boolean; message: string }> {
  const spreadsheetId = process.env.GOOGLE_MANAGEMENT_SHEETS_ID;
  if (!spreadsheetId) {
    return { success: false, message: "Server configuration error." };
  }
  try {
    const auth = await authenticateGoogleOAuth2();
    if (!auth) {
      throw new Error("Authentication failed");
    }
    const sheets = google.sheets({ version: "v4", auth });
    const targetYear = monthYear.split("/")[0];
    const sheetName = `管理費收入${targetYear}`;

    const getResp = await sheets.spreadsheets.values.get({
      spreadsheetId,
      range: `${sheetName}!A:Z`,
    });
    const values = getResp.data.values as (string | number | null)[][] | undefined;
    if (!values || values.length < 2) {
      return { success: false, message: `Sheet '${sheetName}' not found or empty.` };
    }

    const headers = values[0];
    const rows = values.slice(1);
    const colIdx = headers.findIndex((h) => h === monthYear);
    if (colIdx === -1) {
      return { success: false, message: `Column '${monthYear}' not found.` };
    }

    const rowIdx = rows.findIndex((r) => r[1] === residentId);
    if (rowIdx === -1) {
      return { success: false, message: `Resident ID '${residentId}' not found.` };
    }

    const existingValue = rows[rowIdx][colIdx];
    const existingAmount = parseFloat(String(existingValue).replace(/,/g, '')) || 0;
    const finalAmount = existingAmount + amountDelta;

    const sheetRowIdx = rowIdx + 2;
    const sheetColName = String.fromCharCode(65 + colIdx);
    const cellRange = `${sheetName}!${sheetColName}${sheetRowIdx}`;

    await sheets.spreadsheets.values.update({
      spreadsheetId,
      range: cellRange,
      valueInputOption: "USER_ENTERED",
      requestBody: { values: [[finalAmount]] },
    });

    return {
      success: true,
      message: `Updated amount for ${residentId} in ${monthYear} to ${finalAmount}.`,
    };
  } catch (error: any) {
    return { success: false, message: error.message || "Unknown error" };
  }
}

export async function fillManagementSheetAfterAppend(
    appendResult: AppendResult, 
    autoPayData: AutoPayResidentData
) {
    const { action, oldRecords, newRecords } = appendResult;

    if (action === 'skipped') {
        return newRecords.map(r => ({ 
            success: true, 
            message: `Skipped filling for ${r.residentId} due to duplicate ticket.` 
        }));
    }

    const monthYear = autoPayData.processingDate ? formatMonthYear(autoPayData.processingDate) : "";
    if (!monthYear) {
        throw new Error("Could not determine month/year for filling operation.");
    }

    const changes = new Map<string, number>(); // residentId -> amountChange

    if (action === 'replaced' && oldRecords) {
        for (const record of oldRecords) {
            changes.set(record.residentId, (changes.get(record.residentId) || 0) - record.amount);
        }
    }

    for (const record of newRecords) {
        changes.set(record.residentId, (changes.get(record.residentId) || 0) + record.amount);
    }

    const results: { success: boolean; message: string }[] = [];
    for (const [residentId, amountChange] of changes.entries()) {
        if (amountChange === 0) continue;
        const result = await updateResidentAmount({ 
            residentId, 
            monthYear, 
            amountDelta: amountChange 
        });
        results.push(result);
    }
    return results;
}

function formatMonthYear(date: string): string {
  const match = date.match(/(\d{4})年(\d{2})月/);
  if (!match) return "";
  const year = match[1];
  const month = match[2];
  return `${year}/${month}`;
}