"use server";

import { auth } from "@clerk/nextjs/server"; // For server-side auth
import { google } from "googleapis";
import { authenticateGoogleOAuth2 } from "@/lib/google/authenticate";
import { parse } from "csv-parse/sync";
import { prisma } from "@/lib/db";
import { Prisma } from "@prisma/client";
import { storeMonthlyTotals } from "@/actions/account/store-monthly-totals";
import { storeLastDayBalances } from "@/actions/balances/store-last-day-balances";
import { suggestCategory } from "@/lib/utils/transaction-categorization";
import { 
  calculateTransactionFullHash
 } from "@/lib/utils/hash-calculation";
 import { csvMetadataExtraction } from "@/lib/utils/csv-metadata-extraction";
 import { getEndOfMonthDate } from "@/lib/utils/get-end-of-month-date";

{
  /** 
  掃描社區銀行帳戶明細表(CSV檔)，
  並上傳分析結果至Google Sheets。
  1. 收入
    - 管理費收入：depositsMaintenanceFee: 
        - 依照交易資訊(轉帳末6碼),備註(住戶編號)，每住戶管理費按年月塡入表單： 管理費收支帳[管理費收入]。
    - 其他收入：
        - depositsSmallAmount: 小於974元收入，如臨停、汽機車清潔費收入。  
        - depositsUnmatched: 974
  3. 支出
    - withdrawals: 依交易資訊和備註欄之關鍵字(specialKeywords)列出支出項目。
    - withdrawalsUnmatched：交易資訊和備註欄為空白，如清潔人員薪資、零用金等。
  4. 每月累計餘額：
    - lastDayBalances：每個月的最後一筆餘額。
  5. 每月收入和支出總合
    - monthlyDeposits: 每個月所有存入加總金額。
    - monthlyWithdrawals: 每個月所有提出加總金額。
*/
}

interface MonthBalance {
  date: string;
  balance: number;
  fullDateTime: string;
  rawData: CsvRow;
  isLastDayOfMonth: boolean;
}

interface MonthlyTotals {
  [yearMonth: string]: {
    deposits: number;
    withdrawals: number;
  };
}

function normalizeFullWidthChars(text: string | null | undefined): string {
  if (text === null || text === undefined) {
    return "";
  }
  const fullWidthMap: { [key: string]: string } = {
    "０": "0",
    "１": "1",
    "２": "2",
    "３": "3",
    "４": "4",
    "５": "5",
    "６": "6",
    "７": "7",
    "８": "8",
    "９": "9",
    "－": "-",
    "＿": "_",
    "／": "/",
    "（": "(",
    "）": ")",
    "　": " ",
    "Ｆ": "F",
    "ｆ": "f",
  };
  return text
    .split("")
    .map((char) => fullWidthMap[char] || char)
    .join("");
}

function parseIdFromRemark(
  remarkStr: string | null | undefined,
  allCanonicalIdsSet: Set<string>,
  amount?: number
): string | null {
  if (!remarkStr) return null;
  let normalizedRemark = normalizeFullWidthChars(remarkStr);
  // Remove all whitespace for robust matching
  const normalizedRemarkNoSpace = normalizedRemark.replace(/\s+/g, "");
  // Normalize search strings as well
  const search711 = normalizeFullWidthChars("７１１建中門市").replace(
    /\s+/g,
    ""
  );
  const searchJianzhong = normalizeFullWidthChars("建中").replace(/\s+/g, "");
  // Regex for full word '建中' (not part of a longer word)
  const jianzhongRegex = new RegExp(`(^|[^\])${searchJianzhong}([^\]]|$)`);
  // Debug log for special condition
  if (
    (normalizedRemarkNoSpace.includes(search711) ||
      jianzhongRegex.test(normalizedRemarkNoSpace)) &&
    typeof amount === "number"
  ) {
    console.log("[parseIdFromRemark] Special condition check:", {
      normalizedRemark,
      normalizedRemarkNoSpace,
      amount,
    });
  }
  // Special condition: if remark contains '７１１建中門市' or '建中' (as a word) and amount > 6000, assign to 06-01-1
  if (
    (normalizedRemarkNoSpace.includes(search711) ||
      jianzhongRegex.test(normalizedRemarkNoSpace)) &&
    typeof amount === "number" &&
    amount > 6000
  ) {
    return "06-01-1";
  }
  let match = normalizedRemark.match(/(\d{1,2})[-_ /](\d{1,2})[-_ /](\d)/);
  if (match) {
    const b = match[1].padStart(2, "0");
    const f = match[2].padStart(2, "0");
    const u = match[3];
    const candidateId = `${b}-${f}-${u}`;
    if (allCanonicalIdsSet.has(candidateId)) return candidateId;
  }
  const chiNumMap = new Map<string, string>([
    ["一", "1"],
    ["二", "2"],
    ["三", "3"],
    ["四", "4"],
    ["五", "5"],
    ["六", "6"],
    ["七", "7"],
    ["八", "8"],
    ["九", "9"],
    ["十", "10"],
    ["0", "0"],
    ["1", "1"],
    ["2", "2"],
    ["3", "3"],
    ["4", "4"],
    ["5", "5"],
    ["6", "6"],
    ["7", "7"],
    ["8", "8"],
    ["9", "9"],
  ]);
  const textPattern =
    /([0-9０-９一二三四五六七八九十]+)\s*號\s*([0-9０-９一二三四五六七八九十]+)\s*(?:樓|[Ff])(?:之)?\s*[-－]?\s*([0-9０-９一二三四五六七八九十])/;
  const textMatch = normalizedRemark.match(textPattern);

  // Debug logging for specific case
  if (normalizedRemark.includes("69") && normalizedRemark.includes("4")) {
    console.log("[parseIdFromRemark] Debug for 69-04 pattern:", {
      originalRemark: remarkStr,
      normalizedRemark,
      textPattern: textPattern.toString(),
      textMatch,
    });
  }

  if (textMatch) {
    const bText = textMatch[1];
    const fText = textMatch[2];
    const uText = textMatch[3];
    try {
      const bDigit = normalizeFullWidthChars(bText)
        .split("")
        .map((char) => chiNumMap.get(char) || char)
        .join("");
      const fDigit = normalizeFullWidthChars(fText)
        .split("")
        .map((char) => chiNumMap.get(char) || char)
        .join("");
      const uDigit = normalizeFullWidthChars(uText)
        .split("")
        .map((char) => chiNumMap.get(char) || char)
        .join("");
      if (
        /^\d+$/.test(bDigit) &&
        /^\d+$/.test(fDigit) &&
        /^\d+$/.test(uDigit)
      ) {
        const candidateId = `${bDigit.padStart(2, "0")}-${fDigit.padStart(2, "0")}-${uDigit}`;

        // Debug logging for specific case
        if (bDigit === "69" && fDigit === "4") {
          console.log("[parseIdFromRemark] Generated candidate ID for 69-04:", {
            bDigit, fDigit, uDigit, candidateId,
            hasInSet: allCanonicalIdsSet.has(candidateId),
            setSize: allCanonicalIdsSet.size
          });
        }

        if (allCanonicalIdsSet.has(candidateId)) return candidateId;
      }
    } catch (e) {
      /* Ignore */
    }
  }

  // Special cases for 6-1 and 8-1 patterns
  const specialPattern = /\b([68])-1\b/;
  const specialMatch = normalizedRemark.match(specialPattern);
  if (specialMatch) {
    const building = specialMatch[1];
    const candidateId = building === "6" ? "06-01-1" : "08-01-1";

    // Debug logging for special cases
    console.log("[parseIdFromRemark] Special case match:", {
      originalRemark: remarkStr,
      normalizedRemark,
      building,
      candidateId,
      hasInSet: allCanonicalIdsSet.has(candidateId)
    });

    if (allCanonicalIdsSet.has(candidateId)) return candidateId;
  }

  // 6-digit typo: e.g. 711001 → 71-10-1 (user added extra 0 to last digit)
  const sixDigitPattern = /\b(\d{6})\b/g;
  let sixMatch;
  while ((sixMatch = sixDigitPattern.exec(normalizedRemark)) !== null) {
    const numStr = sixMatch[1];
    // Try to match 71-10-1 pattern with extra 0
    // e.g. 711001 → 71-10-1 (if 5th digit is 0, drop it)
    if (
      (numStr.startsWith("69") || numStr.startsWith("71")) &&
      numStr[4] === "0"
    ) {
      // 71 10 01 → 71-10-1
      const b = numStr.substring(0, 2);
      const f = numStr.substring(2, 4);
      const u = numStr.substring(5, 6); // last digit
      const candidateId = `${b}-${f}-${u}`;
      if (allCanonicalIdsSet.has(candidateId)) return candidateId;
    }
  }
  const numPattern = /\b(\d{3,5})\b/g;
  let numMatches;
  while ((numMatches = numPattern.exec(normalizedRemark)) !== null) {
    const numStr = numMatches[1];
    let candidateId: string | null = null;
    if (numStr.length === 3) {
      const b = numStr.charAt(0);
      const f = numStr.charAt(1);
      const u_val = numStr.charAt(2);
      if (b === "6" || b === "8") candidateId = `0${b}-0${f}-${u_val}`;
    } else if (numStr.length === 4) {
      const b_2d = numStr.substring(0, 2);
      const f_1d = numStr.charAt(2);
      const u_val = numStr.charAt(3);
      if (b_2d === "69" || b_2d === "71")
        candidateId = `${b_2d}-0${f_1d}-${u_val}`;
      else {
        const b_1d = numStr.charAt(0);
        const f_2d = numStr.substring(1, 3);
        if (b_1d === "6" || b_1d === "8")
          candidateId = `0${b_1d}-${f_2d}-${u_val}`;
      }
    } else if (numStr.length === 5) {
      const b = numStr.substring(0, 2);
      const f = numStr.substring(2, 4);
      const u_val = numStr.charAt(4);
      if (b === "69" || b === "71") candidateId = `${b}-${f}-${u_val}`;
    }
    if (candidateId && allCanonicalIdsSet.has(candidateId)) return candidateId;
  }
  return null;
}

// --- Hardcoded Resident IDs ---
const allResidentIds = new Set([
  "06-01-1",  "06-02-1",  "06-02-2",  "06-03-1",  "06-03-2",  "06-04-1",  "06-04-2",  "06-05-1",  "06-05-2",  "06-06-1",
  "06-06-2",  "06-07-1",  "06-07-2",  "06-08-1",  "06-08-2",  "06-09-1",  "06-09-2",  "06-10-1",  "06-10-2",  "06-11-1",
  "06-11-2",  "06-12-1",  "08-01-1",  "08-02-1",  "08-02-2",  "08-03-1",  "08-03-2",  "08-04-1",  "08-04-2",  "08-05-1",
  "08-05-2",  "08-06-1",  "08-06-2",  "08-07-1",  "08-07-2",  "08-08-1",  "08-08-2",  "08-09-1",  "08-09-2",  "08-10-1",
  "08-10-2",  "08-11-1",  "08-11-2",  "69-02-1",  "69-02-2",  "69-03-1",  "69-03-2",  "69-04-1",  "69-04-2",  "69-05-1",
  "69-05-2",  "69-06-1",  "69-06-2",  "69-07-1",  "69-07-2",  "69-08-1",  "69-08-2",  "69-09-1",  "69-09-2",  "69-10-1",
  "69-10-2",  "69-11-1",  "69-11-2",  "71-02-1",  "71-02-2",  "71-02-3",  "71-02-5",  "71-02-6",  "71-02-8",  "71-03-1",
  "71-03-2",  "71-03-3",  "71-03-5",  "71-03-6",  "71-03-8",  "71-04-1",  "71-04-2",  "71-04-3",  "71-04-5",  "71-04-6",
  "71-04-8",  "71-05-1",  "71-05-2",  "71-05-3",  "71-05-5",  "71-05-6",  "71-05-8",  "71-06-1",  "71-06-2",  "71-06-3",
  "71-06-5",  "71-06-6",  "71-06-8",  "71-07-1",  "71-07-2",  "71-07-3",  "71-07-5",  "71-07-6",  "71-07-8",  "71-08-1",
  "71-08-2",  "71-08-3",  "71-08-5",  "71-08-6",  "71-08-8",  "71-09-1",  "71-09-2",  "71-09-3",  "71-09-5",  "71-09-6",
  "71-09-8",  "71-10-1",  "71-10-2",  "71-10-3",  "71-10-5",  "71-10-6",  "71-10-8",  "71-11-1",  "71-11-2",  "71-11-3",
  "71-11-5",  "71-11-6",  "71-11-8",
]);

const RESIDENT_PATTERNS = new Map([
  ['split:06-11-1,08-11-1', [/053105/]], // Same owner
  ['split:71-02-1,71-03-5', [/366273/]], // Same owner
  ['06-01-1', [/438530/]],
  ['06-02-1', [/108087/]],
  ['06-02-2', [/158931/]],
  ['06-03-1', [/024312/]],
  ['06-04-1', [/150661/]],
  ['06-04-2', [/077204/]],
  ['06-05-2', [/095111/]],
  ['06-06-1', [/167236/]],
  ['06-07-2', [/077840/]],
  ['06-09-1', [/499730/]],
  ['06-11-2', [/048683/]],
  ['08-02-1', [/009729/]],
  ['08-02-2', [/380320/]],
  ['08-03-1', [/851891/]],
  ['08-04-1', [/940206/]],
  ['08-07-2', [/192300/, /055904/]],
  ['08-08-1', [/305414/]],
  ['08-08-2', [/686211/]],
  ['08-09-1', [/008449/, /687487/]],
  ['08-10-2', [/020571/]],
  ['08-11-2', [/436579/]],
  ['69-02-2', [/022009/]],
  ['69-03-1', [/489587/]],
  ['69-03-2', [/602805/]],
  ['69-05-1', [/122528/]],
  ['69-07-1', [/141612/]],
  ['69-07-2', [/200074/]],
  ['69-08-1', [/201470/]],
  ['69-10-1', [/126065/]],
  ['69-11-1', [/510297/]], // 新屋主
  ['69-11-2', [/435201/]],
  ['71-02-6', [/041804/]],
  ['71-03-1', [/395048/]],
  ['71-03-2', [/374087/]],
  ['71-03-3', [/213575/]],
  ['71-03-6', [/000115/, /074001/]], // Multiple patterns for same resident
  ['71-04-1', [/363291/]],
  ['71-04-3', [/325711/]],
  ['71-05-1', [/173203/]],
  ['71-05-5', [/499934/, /040578/]], // Multiple patterns for same resident
  ['71-06-2', [/667507/]],
  ['71-06-3', [/790712/]],
  ['71-06-6', [/894115/]],
  ['71-07-3', [/237284/]],
  ['71-07-5', [/263630/]],
  ['71-07-6', [/035458/]],
  ['71-07-8', [/221505/]],
  ['71-08-5', [/489533/]],
  ['71-08-6', [/830168/]],
  ['71-09-2', [/402654/]],
  ['71-10-1', [/008494/]],
  ['71-10-2', [/231291/, /056874/, /074173/]], // Multiple patterns for same resident
  ['71-10-3', [/029559/]],
  ['71-10-6', [/222616/]],
  ['71-10-8', [/928655/]],
  ['71-11-1', [/100491/]],
  ['71-11-2', [/075385/]],
  ['71-11-3', [/885119/]],
  ['71-11-5', [/073400/]],
  ['71-11-6', [/998228/]],
  ['71-11-8', [/231742/]],
]);

const WITHDRAWALS_SPECIAL_KEYWORDS = [
 "東聯", "清潔", "冠福", "強迅", "日立", "東聯保全", "社區清潔", "冠福機電保養", "冠福機電維修", "零用金", "社區公共意外險",
  "電話費", "電費", "水費", "區權會出席費", "退裝潢保證金", "0000025035077777"
];

type CsvRow = {
  交易日期: string;
  交易資訊: string;
  帳務日期: string;
  說明: string;
  提出: string;
  存入: string;
  餘額: string;
  備註: string;
};

const MONTH_ABBREVIATIONS = [
  "01",  "02",  "03",  "04",  "05",  "06",  "07",  "08",  "09",  "10",  "11",  "12",
];

// Define reusable base types
type BaseTransaction = {
  transactionDate: string;
  amount: any;
  remark: string;
  row: CsvRow;
  reason?: string;
}

type CreditTransaction = BaseTransaction & {
  type: "CREDIT";
}

type DebitTransaction = BaseTransaction & {
  type: "DEBIT"; 
}

// Extend globalThis to allow properties
declare global {
  // eslint-disable-next-line no-var
  var depositsUnmatched: CreditTransaction[] | undefined;
  var depositsSmallAmount: CreditTransaction[] | undefined;  
  var depositsSpecial: CreditTransaction[] | undefined;
  var withdrawals: DebitTransaction[];
  var withdrawalsUnmatched: DebitTransaction[] | undefined;
  var monthlyTotals: MonthlyTotals | undefined;
  var motorBikeParkingLot: CreditTransaction[] | undefined;
  var lastDayBalances: { [monthYear: string]: MonthBalance } | undefined;
  var depositsMaintenanceFee: { 
    [residentId: string]: { 
      [monthYear: string]: number 
    } 
  } | undefined;
  var specialWithdrawals: DebitTransaction[] | undefined;
}

interface UploadResult {
  success: boolean;
  message: string;
  spreadsheetUrl?: string;
  depositsMaintenanceFee?: (string | number | null)[][];
  depositsUnmatched?: CreditTransaction[];
  depositsSmallAmount?: CreditTransaction[];
  depositsSpecial?: CreditTransaction[];
  motorBikeParkingLot?: CreditTransaction[];
  withdrawals?: DebitTransaction[];
  withdrawalsUnmatched?: DebitTransaction[];
}

// Define DEPOSITS_SPECIAL_KEYWORDS array
const DEPOSITS_SPECIAL_KEYWORDS = [
  "存款息",
  "168888",
  "僑＊福華社區管理委員會",
  "信義房屋",
  "信義",
  "永慶房屋",
  "永慶",
  "全國數位",
];

export async function uploadDepositToSheets(
  csvContent: string,
  enableUpdate: boolean
): Promise<UploadResult> {
  const { userId: clerkUserId } = await auth();
  if (!clerkUserId) {
    return { success: false, message: "Unauthorized: Please sign in." };
  }

  // Extract the accountId once at the start for reuse
  const [accountId, endDate] = csvMetadataExtraction(csvContent)
  const monthEndInfo = getEndOfMonthDate(endDate);
  console.log("Processing account:", accountId);
  console.log("Month end safety check:", {
    endDate,
    isAlreadyEndOfMonth: monthEndInfo.isAlreadyEndOfMonth,
    isSafeToUpdate: monthEndInfo.isSafeToUpdate,
    currentDate: monthEndInfo.currentDate,
    endOfMonthDate: monthEndInfo.endOfMonthDate
  });

  const spreadsheetId = process.env.GOOGLE_MANAGEMENT_SHEETS_ID!;
  if (!spreadsheetId) {
    console.error(
      "Google Sheets Spreadsheet ID (GOOGLE_MANAGEMENT_SHEETS_ID) not configured."
    );
    return {
      success: false,
      message: "Server configuration error. Please contact support.",
    };
  }

  // Reset global variables to avoid duplicates across runs
  globalThis.withdrawals = [];
  globalThis.depositsUnmatched = [];
  globalThis.depositsSmallAmount = [];
  globalThis.depositsSpecial = [];
  globalThis.withdrawalsUnmatched = [];
  globalThis.motorBikeParkingLot = [];
  globalThis.monthlyTotals = {};

  try {
    /*const googleAuth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!,
        private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY!?.replace(
          /\\n/g,
          "\n"
        ),
      },
      scopes: ["https://www.googleapis.com/auth/spreadsheets"],
    });*/
    const googleAuth = await authenticateGoogleOAuth2();
    if (!googleAuth) {
      throw new Error("Authentication failed");
    }
    const sheets = google.sheets({ version: "v4", auth: googleAuth });

    // Clean and prepare CSV content for parsing
    function prepareCsvContent(content: string): string {
      // Split into lines but preserve newlines within quoted fields
      const lines: string[] = [];
      let currentLine = "";
      let insideQuotes = false;

      for (let i = 0; i < content.length; i++) {
        const char = content[i];

        if (char === '"') {
          insideQuotes = !insideQuotes;
        }

        if (char === "\n" && !insideQuotes) {
          if (currentLine.trim()) {
            lines.push(currentLine);
          }
          currentLine = "";
        } else {
          currentLine += char;
        }
      }
      if (currentLine.trim()) {
        lines.push(currentLine);
      }

      // Find the header line
      const headerIndex = lines.findIndex(
        (line) =>
          line.includes("交易日期") &&
          line.includes("帳務日期") &&
          line.includes("說明") &&
          line.includes("提出") &&
          line.includes("存入") &&
          line.includes("餘額") &&
          line.includes("交易資訊") &&
          line.includes("備註")
      );

      if (headerIndex === -1) return "";

      // Keep only the header and data lines
      const relevantLines = lines.slice(headerIndex);

      // Clean each line
      const cleanedLines = relevantLines
        .map((line) => {
          // Normalize spaces and handle quotes
          return line
            .replace(/\s+/g, " ") // normalize multiple spaces
            .replace(/""/g, '"') // handle escaped quotes
            .trim();
        })
        .join("\n");

      return cleanedLines;
    }

    // Use the new prepareCsvContent function to clean and normalize the CSV
    const cleanCsvContent = prepareCsvContent(csvContent);
    if (!cleanCsvContent) {
      return {
        success: false,
        message:
          "Could not find valid CSV headers. Expected: 交易日期, 帳務日期, 說明, 提出, 存入, 餘額, 交易資訊, 備註",
      };
    }

    // Parse the cleaned content
    const records: CsvRow[] = parse(cleanCsvContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true,
      quote: '"',
      escape: '"',
      relax_quotes: true,
      relax_column_count: true,
      skip_records_with_error: true,
    });

    console.log("Parsed records count:", records.length);
    if (records.length > 0) {
      console.log("First record:", records[0]);
      console.log("Record keys:", Object.keys(records[0]));
    }

    const allCanonicalIds: string[] = Array.from(allResidentIds).sort();
    const allCanonicalIdsSet = new Set(allCanonicalIds);

    // Set up data structures for parallel processing results
    // Note: depositsMaintenanceFee data will be stored in globalThis.depositsMaintenanceFee

    let targetYear = new Date().getFullYear();
    if (records.length > 0 && records[0]["交易日期"]) {
      const dateStr = String(records[0]["交易日期"]).split(/[\s\n]/)[0];
      const dateParts = dateStr.split("/");
      if (dateParts.length >= 2) {
        const year = parseInt(dateParts[0]);
        if (!isNaN(year)) {
          targetYear = year;
        }
      }
    }

    console.log("Target year:", targetYear);

    // Declare withdrawalsData to collect special withdrawal rows for reporting
    let withdrawalsData:
      | Array<{
          transactionDate: string;
          type: "DEBIT" | "CREDIT";
          remark: string;
          amount: string | number;
          row: CsvRow;
        }>
      | undefined;
    // Declare lastDayBalances to store the last day balance for each month
    const lastDayBalances: { [monthYear: string]: MonthBalance } = {};
   
    // Main processing function
    async function processRecordsInParallel() {
      // Set up data structures
      // Note: lastDayBalances is already declared in the outer scope
      
      // Get target year
      let targetYear = new Date().getFullYear();
      if (records.length > 0 && records[0]["交易日期"]) {
        const dateStr = String(records[0]["交易日期"]).split(/[\s\n]/)[0];
        const dateParts = dateStr.split("/");
        if (dateParts.length >= 2) {
          const year = parseInt(dateParts[0]);
          if (!isNaN(year)) {
            targetYear = year;
          }
        }
      }

      console.log("Target year:", targetYear);

      // Initialize globalThis arrays to ensure they're clean
      globalThis.depositsUnmatched = [];
      globalThis.depositsSmallAmount = [];
      globalThis.depositsSpecial = [];
      globalThis.withdrawals = [];
      globalThis.withdrawalsUnmatched = [];
      globalThis.motorBikeParkingLot = [];

      // Pre-filter records by type
      const depositRecords = records.filter(row =>
        row["存入"] && row["存入"].trim() !== "−"
      );
      const withdrawalRecords = records.filter(row =>
        row["提出"] && row["提出"].trim() !== "−"
      );

      console.log("[DEBUG] Records summary before processing:", {
        totalRecords: records.length,
        depositRecords: depositRecords.length,
        withdrawalRecords: withdrawalRecords.length,
        sampleRecord: records[0],
        lastRecord: records[records.length - 1]
      });

      // Process in parallel
      const [monthlyTotals, balances, deposits, withdrawals, motorBikeParkingLot] = await Promise.all([
        // Calculate monthly totals
        (async () => {
          const totals = calculateMonthlyTotals(records);
          console.log("Monthly totals calculated:", totals);
          return totals;
        })(),

        // Process last day balances
        processLastDayBalances(records),

        // Process deposits
        processDeposits(depositRecords, allCanonicalIdsSet, targetYear),

        // Process withdrawals
        processWithdrawals(withdrawalRecords),

        // Process motor bike parking lot
        processMotorBikeParkingLot(depositRecords)
      ]);

      // Handle split payments
      const splitAdjustments: { [residentId: string]: { [monthYear: string]: number } } = {};

      for (const key in deposits) {
        if (key.startsWith('split:')) {
          const residentIds = key.substring(6).split(',');
          const monthlyPayments = deposits[key];
      
          for (const monthYear in monthlyPayments) {
            const totalAmount = monthlyPayments[monthYear];
      
            // Special logic for 06-11-1 and 08-11-1
            if (residentIds.length === 2 && residentIds.includes('06-11-1') && residentIds.includes('08-11-1')) {
              const baseAmount = 1958;
              const k = Math.round((totalAmount / 2) / baseAmount);
              const amountFor06111 = k * baseAmount;
              const amountFor08111 = totalAmount - amountFor06111;
      
              // Adjustments for 06-11-1
              if (!splitAdjustments['06-11-1']) splitAdjustments['06-11-1'] = {};
              if (!splitAdjustments['06-11-1'][monthYear]) splitAdjustments['06-11-1'][monthYear] = 0;
              splitAdjustments['06-11-1'][monthYear] += amountFor06111;
      
              // Adjustments for 08-11-1
              if (!splitAdjustments['08-11-1']) splitAdjustments['08-11-1'] = {};
              if (!splitAdjustments['08-11-1'][monthYear]) splitAdjustments['08-11-1'][monthYear] = 0;
              splitAdjustments['08-11-1'][monthYear] += amountFor08111;
      
            } else if (residentIds.length === 2 && residentIds.includes('71-02-1') && residentIds.includes('71-03-5')) {
              const baseAmount1 = 1904; // for 71-02-1
              const baseAmount2 = 2641; // for 71-03-5
              const totalBase = baseAmount1 + baseAmount2;
              const n = Math.round(totalAmount / totalBase);

              if (n > 0) {
                const amountFor71021 = n * baseAmount1;
                const amountFor71035 = totalAmount - amountFor71021;
        
                // Adjustments for 71-02-1
                if (!splitAdjustments['71-02-1']) splitAdjustments['71-02-1'] = {};
                if (!splitAdjustments['71-02-1'][monthYear]) splitAdjustments['71-02-1'][monthYear] = 0;
                splitAdjustments['71-02-1'][monthYear] += amountFor71021;
        
                // Adjustments for 71-03-5
                if (!splitAdjustments['71-03-5']) splitAdjustments['71-03-5'] = {};
                if (!splitAdjustments['71-03-5'][monthYear]) splitAdjustments['71-03-5'][monthYear] = 0;
                splitAdjustments['71-03-5'][monthYear] += amountFor71035;
              } else {
                // Fallback to equal split if calculation is not reasonable
                const splitAmount = totalAmount / residentIds.length;
                for (const residentId of residentIds) {
                  if (!splitAdjustments[residentId]) splitAdjustments[residentId] = {};
                  if (!splitAdjustments[residentId][monthYear]) splitAdjustments[residentId][monthYear] = 0;
                  splitAdjustments[residentId][monthYear] += splitAmount;
                }
              }
            } else {
              // Generic equal-split logic
              const splitAmount = totalAmount / residentIds.length;
              for (const residentId of residentIds) {
                if (!splitAdjustments[residentId]) splitAdjustments[residentId] = {};
                if (!splitAdjustments[residentId][monthYear]) splitAdjustments[residentId][monthYear] = 0;
                splitAdjustments[residentId][monthYear] += splitAmount;
              }
            }
          }
          delete deposits[key];
        }
      }
      
      // Apply adjustments
      for (const residentId in splitAdjustments) {
        if (!deposits[residentId]) deposits[residentId] = {};
        for (const monthYear in splitAdjustments[residentId]) {
          deposits[residentId][monthYear] = (deposits[residentId][monthYear] || 0) + splitAdjustments[residentId][monthYear];
        }
      }

      // Assign results to global variables and local variables
      globalThis.monthlyTotals = monthlyTotals;
      globalThis.lastDayBalances = balances;
      globalThis.depositsMaintenanceFee = deposits;
      globalThis.specialWithdrawals = withdrawals;
      // motorBikeParkingLot is already assigned in the processMotorBikeParkingLot function
      //console.log("depositsMaintenaceFee", deposits);
      //console.log("globalThis.depositsMaintenanceFee", globalThis.depositsMaintenanceFee);

      Object.assign(lastDayBalances, balances);

      // depositsMaintenanceFee is now stored in globalThis.depositsMaintenanceFee
      
      // Note: globalThis arrays are already populated by the individual processing functions
      // But you can also access them here if needed for additional processing
      
      /*console.log("All processing completed in parallel");
      console.log("Results summary:");
      console.log("- Monthly totals:", Object.keys(monthlyTotals).length, "months");
      console.log("- Last day balances:", Object.keys(balances).length, "months");
      console.log("- Deposits matched:", Object.keys(deposits).length, "residents");
      console.log("- Deposits unmatched:", globalThis.depositsUnmatched?.length || 0);
      console.log("- Deposits small amount:", globalThis.depositsSmallAmount?.length || 0);
      console.log("- Special withdrawals:", globalThis.withdrawals?.length || 0);
      console.log("- Withdrawals unmatched:", globalThis.withdrawalsUnmatched?.length || 0);
      */

      //console.log("- Monthly totals calculated:", globalThis.monthlyTotals);
      //console.log("////////////////////Final deposits data:////////////////////", depositsMaintenanceFee);
      //console.log("////////////////////depositsSmallAmount////////////////////", globalThis.depositsSmallAmount);
      //console.log("////////////////////depositsSpecial////////////////////", globalThis.depositsSpecial);
      //console.log("////////////////////Final withdrawals data:////////////////////", globalThis.withdrawals);
      //console.log("////////////////////globalThis.withdrawalsUnmatched////////////////////", globalThis.withdrawalsUnmatched);
      //console.log("////////////////////globalThis.depositsUnmatched////////////////////", globalThis.depositsUnmatched);

      // Store monthly totals to bankAccount transaction:
      if (enableUpdate) {
        const storeResult = await storeMonthlyTotals({
          originalId: accountId,
          monthlyTotals: globalThis.monthlyTotals!
        });

        if (!storeResult.success) {
          console.error("Failed to store monthly totals:", storeResult.error);
        } else {
          console.log("Monthly totals stored successfully");
        }        
      }
    }
    await processRecordsInParallel();
    
   
    const sheetName =`管理費收入${targetYear}`;
    const getResp = await sheets.spreadsheets.values.get({
      spreadsheetId,
      range: `${sheetName}!A:Z`,
    });
    const values = getResp.data.values as
      | (string | number | null)[][]
      | undefined;
    if (!values || values.length < 2) {
      return { success: false, message: "Sheet data not found or empty." };
    }
    const headers = values[0];
    const rows = values.slice(1);

    // Collect all cell updates for batch update
    const batchUpdates: Array<{ range: string; values: any[][] }> = [];
    const updatedCells: Array<{
      residentId: string;
      monthYear: string;
      amount: number;
    }> = [];

    // Check if "機踏車位" column already exists
    const motorBikeDeductionColName = "機踏車位";
    const existingMotorBikeColIdx = headers.findIndex((h) => h === motorBikeDeductionColName);

    console.log("Motor bike deduction column check:", {
      columnName: motorBikeDeductionColName,
      existingIndex: existingMotorBikeColIdx,
      exists: existingMotorBikeColIdx !== -1
    });

    // Calculate motor bike parking deductions by resident
    const motorBikeDeductions: { [residentId: string]: number } = {};

    /*console.log("[DEBUG] Motor bike parking lot processing:", {
      hasMotorBikeParkingLot: !!globalThis.motorBikeParkingLot,
      motorBikeParkingLotLength: globalThis.motorBikeParkingLot?.length || 0,
      motorBikeParkingLotData: globalThis.motorBikeParkingLot
    });*/

    if (globalThis.motorBikeParkingLot) {
      //console.log("[DEBUG] Processing motor bike parking entries...");

      for (const entry of globalThis.motorBikeParkingLot) {
        // Extract resident ID from the transaction data
        const remark = entry.remark || "";
        const transactionInfo = entry.row?.交易資訊 || "";

        /*console.log("[DEBUG] Processing motor bike entry:", {
          entry,
          remark,
          transactionInfo,
          amount: entry.amount
        });*/

        // Try to parse resident ID from the motor bike parking entry
        const parsedRid = parseIdFromRemark(remark, allCanonicalIdsSet, entry.amount) ||
                         findResidentByPattern(entry.row?.交易資訊 || '', remark, entry.amount);

        /*console.log("[DEBUG] Parsed resident ID:", {
          remark,
          transactionInfo,
          amount: entry.amount,
          parsedRid
        });*/

        if (parsedRid) {
          motorBikeDeductions[parsedRid] = (motorBikeDeductions[parsedRid] || 0) + entry.amount;
          /*console.log("[DEBUG] Added to motorBikeDeductions:", {
            residentId: parsedRid,
            amount: entry.amount,
            totalForResident: motorBikeDeductions[parsedRid]
          });*/
        } else {
          console.log("[DEBUG] Failed to parse resident ID for motor bike entry:", {
            remark,
            transactionInfo,
            amount: entry.amount
          });
        }
      }
    } else {
      console.log("[DEBUG] No motorBikeParkingLot data found in globalThis");
    }

    //console.log("[DEBUG] Final motorBikeDeductions:", motorBikeDeductions);

    //console.log("depositsMaintenanceFee: ==================>", globalThis.depositsMaintenanceFee);
    for (const residentId of Object.keys(globalThis.depositsMaintenanceFee || {})) {
      const residentPayments = globalThis.depositsMaintenanceFee![residentId];
      const rowIdx = rows.findIndex((r) => r[1] === residentId);
      if (rowIdx === -1) {
        console.log("Resident not found in sheet:", residentId);
        continue;
      }
      const sheetRowIdx = rowIdx + 2;

      // Update maintenance fee columns
      for (const monthYear of Object.keys(residentPayments)) {
        const colIdx = headers.findIndex((h) => h === monthYear);
        if (colIdx === -1) {
          console.log("Month column not found:", monthYear);
          continue;
        }
        const sheetColIdx = colIdx + 1;
        const cellRange = `${sheetName}!${String.fromCharCode(64 + sheetColIdx)}${sheetRowIdx}`;
        const amount = residentPayments[monthYear];

        // Apply update to local rows array
        rows[rowIdx][colIdx] = amount;

        batchUpdates.push({ range: cellRange, values: [[amount]] });
        updatedCells.push({ residentId, monthYear, amount });
      }

      // Add motor bike parking deduction for this resident (only if column exists)
      /*console.log(`[DEBUG] Checking motor bike deduction for resident ${residentId}:`, {
        hasDeduction: !!motorBikeDeductions[residentId],
        deductionAmount: motorBikeDeductions[residentId],
        columnExists: existingMotorBikeColIdx !== -1,
        columnIndex: existingMotorBikeColIdx
      });*/

      if (motorBikeDeductions[residentId] && existingMotorBikeColIdx !== -1) {
        const deductionAmount = motorBikeDeductions[residentId] * -1; // Negative amount for deduction
        const deductionCellRange = `${sheetName}!${String.fromCharCode(65 + existingMotorBikeColIdx)}${sheetRowIdx}`;

        batchUpdates.push({ range: deductionCellRange, values: [[deductionAmount]] });
        //console.log(`[DEBUG] SUCCESS: Adding motor bike deduction for ${residentId}: ${deductionAmount} at ${deductionCellRange}`);
      } else {
        /*if (!motorBikeDeductions[residentId]) {
          console.log(`[DEBUG] SKIP: No motor bike deduction for resident ${residentId}`);
        }*/
        if (existingMotorBikeColIdx === -1) {
          console.log(`[DEBUG] SKIP: Motor bike deduction column does not exist`);
        }
      }
    }

    // (Disable for test)
    // Batch update all cells in one request
    console.log("[DEBUG] Batch update summary:", {
      totalUpdates: batchUpdates.length,
      enableUpdate,
      motorBikeDeductionsCount: Object.keys(motorBikeDeductions).length,
      batchUpdates: batchUpdates.map(update => ({
        range: update.range,
        value: update.values[0][0]
      }))
    });

    if (batchUpdates.length > 0 && enableUpdate) {
      console.log("[DEBUG] Executing batch update to Google Sheets...");
      await sheets.spreadsheets.values.batchUpdate({
        spreadsheetId,
        requestBody: {
          valueInputOption: "USER_ENTERED",
          data: batchUpdates,
        },
      });
      console.log("[DEBUG] Batch update completed successfully");
    } else {
      if (batchUpdates.length === 0) {
        console.log("[DEBUG] No batch updates to perform");
      }
      if (!enableUpdate) {
        console.log("[DEBUG] Updates disabled (enableUpdate = false)");
      }
    }
    // Process depositsSpecial data if available this year
    if (
      globalThis.depositsSpecial &&
      globalThis.depositsSpecial.length > 0 &&
      monthEndInfo.isAlreadyEndOfMonth === true &&
      monthEndInfo.isSafeToUpdate === true &&
      enableUpdate && targetYear === new Date().getFullYear()
    ) {
      const result = await createIncomeFromDeposits(
        globalThis.depositsSpecial,
        accountId
      );
      if (!result.success) {
        console.error("Failed to create income records:", result.error);
      }
    }

    // Process unmatched deposits data if available this year
    if (
      globalThis.depositsUnmatched &&
      globalThis.depositsUnmatched.length > 0 &&
      monthEndInfo.isAlreadyEndOfMonth === true &&
      monthEndInfo.isSafeToUpdate === true &&
      enableUpdate && targetYear === new Date().getFullYear()
    ) {
      const result = await createIncomeFromDeposits(
        globalThis.depositsUnmatched,
        accountId
      );
      if (!result.success) {
        console.error("Failed to create income records:", result.error);
      }
    }

    // Process small amount deposits this year
    if (
      globalThis.depositsSmallAmount &&
      globalThis.depositsSmallAmount.length > 0 &&
      monthEndInfo.isAlreadyEndOfMonth === true &&
      monthEndInfo.isSafeToUpdate === true &&
      enableUpdate && targetYear === new Date().getFullYear()
    ) {
      const result = await createIncomeFromSmallDeposits(
        globalThis.depositsSmallAmount,
        accountId
      );
      //console.log("results===============>")
      if (!result.success) {
        console.error("Failed to create income records from small deposits:", result.error);
      }
    }

    // Process unmatched withdrawals if available this year
    if (
      globalThis.withdrawalsUnmatched &&
      globalThis.withdrawalsUnmatched.length > 0 &&
      monthEndInfo.isAlreadyEndOfMonth === true &&
      monthEndInfo.isSafeToUpdate === true &&
      enableUpdate && targetYear === new Date().getFullYear()
    ) {
      const result = await createExpenseFromWithdrawals(
        globalThis.withdrawalsUnmatched,
        accountId
      );
      if (!result.success) {
        console.error("Failed to create expense records from unmatched withdrawals:", result.error);
      }
    }

    // Process withdrawals data if available this year
    console.log("isAlreadyEndOfMonth, isSafeToUpdate, withdrawals:==================>", {
      isAlreadyEndOfMonth: monthEndInfo.isAlreadyEndOfMonth,
      isSafeToUpdate: monthEndInfo.isSafeToUpdate,
      withdrawals: globalThis.withdrawals
    });
    if (
      globalThis.withdrawals &&
      globalThis.withdrawals.length > 0 &&
      monthEndInfo.isAlreadyEndOfMonth === true &&
      monthEndInfo.isSafeToUpdate === true &&
      enableUpdate && targetYear === new Date().getFullYear()

    ) {
      const result = await createExpenseFromWithdrawals(
        globalThis.withdrawals,
        accountId
      );
      if (!result.success) {
        console.error("Failed to create expense records:", result.error);
      }
    }

    // Store last day balances with only relevant raw data this year
    //console.log("monthEndInfo:==================>", monthEndInfo)
    //console.log("lastDayBalances:==================>", lastDayBalances)

    if (
      enableUpdate &&
      targetYear === new Date().getFullYear() &&
      monthEndInfo.isAlreadyEndOfMonth === true &&
      monthEndInfo.isSafeToUpdate === true &&
      Object.keys(lastDayBalances).length > 0
    ) {
      const relevantRawData = Object.values(lastDayBalances).map(
        (balance) => balance.rawData
      );

      console.log("relevantRawData before storing:==================>", relevantRawData);

      await storeLastDayBalances({
        originalId: accountId,
        balances: lastDayBalances,
        rawData: relevantRawData,
      });
    } else {
      console.log("Skipping storeLastDayBalances due to conditions:", {
        enableUpdate,
        targetYear,
        currentYear: new Date().getFullYear(),
        isAlreadyEndOfMonth: monthEndInfo.isAlreadyEndOfMonth,
        isSafeToUpdate: monthEndInfo.isSafeToUpdate,
        hasBalances: Object.keys(lastDayBalances).length > 0
      });
    }


    //console.log("Updated cells:", updatedCells);

    return {
      success: true,
      message: `CSV data processed and uploaded. Updated ${updatedCells.length} cells.`,
      spreadsheetUrl: `https://docs.google.com/spreadsheets/d/${spreadsheetId}/edit#gid=SHEET_TAB_ID_IF_KNOWN`,
      //updatedCells: updatedCells.map(c => [c.residentId, c.monthYear, c.amount]), // Separate field for updates
      depositsMaintenanceFee: [headers, ...rows],
      depositsUnmatched: globalThis.depositsUnmatched,
      depositsSmallAmount: globalThis.depositsSmallAmount,
      depositsSpecial: globalThis.depositsSpecial,
      motorBikeParkingLot: globalThis.motorBikeParkingLot,
      withdrawals: globalThis.withdrawals || [],
      withdrawalsUnmatched: globalThis.withdrawalsUnmatched,
    };
  } catch (error: any) {
    console.error("Error in uploadDepositToSheets:", error);
    return {
      success: false,
      message: `Upload error: ${error.message || "Unknown server error"}`,
    };
  }
}

// ---  將可歸類支出匯入 bankAccount.expense 資料表 ---
async function createExpenseFromWithdrawals(
  withdrawalsData: Array<{
    transactionDate: string;
    type: "DEBIT" | "CREDIT";
    remark: string;
    amount: string | number;
    row: CsvRow;
  }>,
  accountId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("未經授權");

    // Account mapping based on pattern
    const managementAccountId = process.env.VIRTUAL_ACCOUNT_ID_MANAGEMENT;
    const parkingAccountId = process.env.VIRTUAL_ACCOUNT_ID_CAR_PARKING_SPACE;

    if (!managementAccountId || !parkingAccountId) {
      throw new Error(
        "Virtual account IDs not configured in environment variables"
      );
    }

    const targetAccountId = accountId.includes("************")
      ? managementAccountId
      : parkingAccountId;

    // Process all withdrawals and prepare the data first
    const preparedData = await Promise.all(
      withdrawalsData
        .filter((withdrawal) => withdrawal.remark !== "−") // Filter out transactions with remark "−"
        .map(async (withdrawal) => {
          const amount =
            typeof withdrawal.amount === "string"
              ? parseFloat(withdrawal.amount.replace(/,/g, ""))
              : withdrawal.amount;

          if (isNaN(amount)) return null;

          // Get category suggestion
          const { categoryId: suggestedCategoryId } = await suggestCategory(
            withdrawal.remark,
            amount,
            "DEBIT"
          );

          // Parse and validate date
          const dateParts = withdrawal.transactionDate.split("/");
          if (dateParts.length !== 3) {
            throw new Error(
              `Invalid date format for monthYear: ${withdrawal.transactionDate}`
            );
          }

          const [year, month, day] = dateParts;
          const parsedYear = parseInt(year);
          const parsedMonth = parseInt(month) - 1;
          const parsedDay = parseInt(day);

          if (isNaN(parsedYear) || isNaN(parsedMonth) || isNaN(parsedDay)) {
            throw new Error(
              `Invalid date components for ${withdrawal.transactionDate}`
            );
          }

          // Parse time components (Taipei time UTC+8)
          let hours = 0,
            minutes = 0;
          if (withdrawal.transactionDate) {
            const [_, time] = withdrawal.transactionDate.split(" ");
            if (time) {
              const [h, m] = time.split(":");
              const parsedHours = parseInt(h);
              const parsedMinutes = parseInt(m);

              if (!isNaN(parsedHours) && !isNaN(parsedMinutes)) {
                hours = parsedHours >= 8 ? parsedHours - 8 : parsedHours + 16;
                minutes = parsedMinutes;
              }
            }
          }

          const date = new Date(
            Date.UTC(parsedYear, parsedMonth, parsedDay, hours, minutes, 0)
          );

          return {
            amount,
            suggestedCategoryId,
            date,
            description: withdrawal.remark,
            transactionDate: withdrawal.transactionDate,
          };
        })
    );

    // Filter out any null values from invalid amounts
    const validData = preparedData.filter(
      (data): data is NonNullable<typeof data> => data !== null
    );

    // First ensure we have a default category
    const defaultCategory = await prisma.category.upsert({
      where: {
        name_userId: {
          name: "OTHER",
          userId,
        },
      },
      update: {},
      create: {
        name: "OTHER",
        userId,
        icon: "HelpCircle",
        type: "DEBIT"
      },
    });

    // Execute all database operations in a single transaction
    //await prisma.$transaction(async (tx) => {
    // Process each withdrawal data
    for (const data of validData) {
      // Generate deduplication hash
      const importHash = calculateTransactionFullHash({
        date: data.transactionDate,
        type: "DEBIT",
        categoryId: defaultCategory.id,
        accountId: targetAccountId,
        amount: data.amount,
        description: data.description,
      });

      // Create expense record if it doesn't exist
      await prisma.expense.upsert({
        where: { importHash },
        update: {}, // No update if exists
        create: {
          amount: new Prisma.Decimal(data.amount),
          description: data.description,
          date: data.date,
          categoryId: defaultCategory.id,
          userId,
          accountId: targetAccountId,
          type: "DEBIT",
          categoryValidated: true,
          suggestedCategoryId: data.suggestedCategoryId,
          importHash,
        },
      });
    }
    //});

    return { success: true };
  } catch (error) {
    console.error("Error creating expense records:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to create expense records",
    };
  }
}

// --- 將管理費收入匯入 bankAccount.income 資料表 ---
async function createIncomeFromDeposits(
  depositsData: Array<{
    transactionDate: string;
    type: "DEBIT" | "CREDIT";
    remark: string;
    amount: string | number;
    row: CsvRow;
  }>,
  accountId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("未經授權");

    // Account mapping based on pattern
    const managementAccountId = process.env.VIRTUAL_ACCOUNT_ID_MANAGEMENT;
    const parkingAccountId = process.env.VIRTUAL_ACCOUNT_ID_CAR_PARKING_SPACE;

    if (!managementAccountId || !parkingAccountId) {
      throw new Error("Virtual account IDs not configured in environment variables");
    }

    const targetAccountId = accountId.includes("************")
      ? managementAccountId
      : parkingAccountId;

    // Process all deposits and prepare the data first
    const preparedData = await Promise.all(
      depositsData
        .filter(deposit => 
          !(deposit.remark === "−" && deposit.row.交易資訊 === "−") || // Keep if at least one field has info
          deposit.row.說明.includes("轉帳") && deposit.row.備註.includes("管理費") || // Keep deposit interest info
          deposit.row.說明.includes("存款息") || // Keep deposit interest info
          deposit.row.備註.includes("信義") || // Keep real estate transactions
          deposit.row.備註.includes("永慶") || // Keep real estate transactions
          deposit.row.備註.includes("全國數位") || // Keep cable media transactions
          deposit.row.交易資訊.includes("168888") || // Keep management committee account
          deposit.row.交易資訊.includes("僑＊福華社區管理委員會") // Keep management committee name
        )
        .map(async (deposit) => {
          const amount =
            typeof deposit.amount === "string"
              ? parseFloat(deposit.amount.replace(/,/g, ""))
              : deposit.amount;

          if (isNaN(amount)) return null;

          // Select the best description based on available information
          let description = deposit.remark !== "−"
            ? deposit.remark
            : deposit.row.交易資訊 !== "−"
              ? deposit.row.交易資訊.trim()
              : deposit.row.說明; // Fallback to 說明 if both are "−"

          console.log("rewrite description============================>", deposit.remark, deposit.row.交易資訊, deposit.row.說明);
          // Rewrite specific descriptions to more readable text
          if (description.includes("全國數位有線電視股份有限公司")) {
            description = "全國數位使用電費";
          }
          if (description.includes("(013)0000025***168888 僑＊福華社區管理委員會")) {
            description = "888-8網銀轉帳";
          }
          if (description.includes("信義")) {
            description = "信義房屋公告費";
          }
          /*if (description.includes("永慶")) {
            description = "永慶房屋公告費";
          }*/


          // Get category suggestion
          const { categoryId: suggestedCategoryId } = await suggestCategory(
            description,
            amount,
            "CREDIT"
          );

          // Parse and validate date
          const dateParts = deposit.row.交易日期.split("/");
          if (dateParts.length !== 3) {
            throw new Error(`Invalid date format for monthYear: ${deposit.row.交易日期}`);
          }

          const [year, month, day] = dateParts;
          const parsedYear = parseInt(year);
          const parsedMonth = parseInt(month) - 1;
          const parsedDay = parseInt(day);

          if (isNaN(parsedYear) || isNaN(parsedMonth) || isNaN(parsedDay)) {
            throw new Error(`Invalid date components for ${deposit.transactionDate}`);
          }

          // Parse time components (Taipei time UTC+8)
          let hours = 0, minutes = 0;
          if (deposit.row.交易日期) {
            const [_, time] = deposit.row.交易日期.split(" ");
            if (time) {
              const [h, m] = time.split(":");
              const parsedHours = parseInt(h);
              const parsedMinutes = parseInt(m);

              if (!isNaN(parsedHours) && !isNaN(parsedMinutes)) {
                hours = parsedHours >= 8 ? parsedHours - 8 : parsedHours + 16;
                minutes = parsedMinutes;
              }
            }
          }

          const date = new Date(
            Date.UTC(parsedYear, parsedMonth, parsedDay, hours, minutes, 0)
          );

          return {
            amount,
            suggestedCategoryId,
            date,
            description,
            transactionDate: deposit.transactionDate
          };
        })
    );

    // Filter out any null values from invalid amounts
    const validData = preparedData.filter((data): data is NonNullable<typeof data> => data !== null);

    // First ensure we have a default category for income
    const defaultCategory = await prisma.category.upsert({
      where: {
        name_userId: {
          name: 'INCOME',
          userId,
        },
      },
      update: {},
      create: {
        name: 'INCOME',
        userId,
        icon: 'Briefcase',
        type: "CREDIT"
      },
    });

    // Process accumulated maintenance fees from globalThis.depositsMaintenanceFee
    if (globalThis.depositsMaintenanceFee) {
      console.log("Processing accumulated maintenance fees:", globalThis.depositsMaintenanceFee);

      // Accumulate totals by month
      const monthlyTotals: { [monthYear: string]: number } = {};

      for (const [, monthlyPayments] of Object.entries(globalThis.depositsMaintenanceFee)) {
        for (const [monthYear, amount] of Object.entries(monthlyPayments)) {
          monthlyTotals[monthYear] = (monthlyTotals[monthYear] || 0) + amount;
        }
      }

      console.log("Monthly maintenance fee totals:", monthlyTotals);

      // Create one income record per month with accumulated total
      for (const [monthYear, totalAmount] of Object.entries(monthlyTotals)) {
        // Extract month number from monthYear (e.g., "2025/06" -> "6")
        const monthMatch = monthYear.match(/(\d{4})\/(\d{2})/);
        if (!monthMatch) continue;

        const year = parseInt(monthMatch[1]);
        const monthNum = parseInt(monthMatch[2]);
        const monthName = monthNum.toString(); // "6" for June

        // Create description with dynamic month
        const description = `${monthName}月份現金/轉帳管理費`;

        // Create date for the last day of the month
        const date = new Date(Date.UTC(year, monthNum, 0, 15, 30, 0)); // Last day of month

        // Get category suggestion
        const { categoryId: suggestedCategoryId } = await suggestCategory(
          description,
          totalAmount,
          "CREDIT"
        );

        // Generate deduplication hash
        const importHash = calculateTransactionFullHash({
          date,
          type: "CREDIT",
          categoryId: defaultCategory.id,
          accountId: targetAccountId,
          amount: totalAmount,
          description,
        });

        // Upsert income record (update if exists, create if not)
        await prisma.income.upsert({
          where: { importHash },
          update: {
            amount: new Prisma.Decimal(totalAmount),
            description,
            date,
            categoryId: suggestedCategoryId || defaultCategory.id,
            suggestedCategoryId,
            updatedAt: new Date(),
          },
          create: {
            amount: new Prisma.Decimal(totalAmount),
            description,
            date,
            categoryId: suggestedCategoryId || defaultCategory.id,
            userId,
            accountId: targetAccountId,
            type: "CREDIT",
            categoryValidated: true,
            suggestedCategoryId,
            importHash,
          },
        });

        console.log(`Processed accumulated maintenance fee: ${description} - Total: ${totalAmount}`);
      }
    }

    // Process each deposit data
    console.log("----------------------------preparedData-----------------------------", preparedData)
    console.log("----------------------------validateData-----------------------------", validData)
    for (const data of validData) {
      // Generate deduplication hash
      const importHash = calculateTransactionFullHash({
        date: data.date,
        type: "CREDIT",
        categoryId: defaultCategory.id,
        accountId: targetAccountId,
        amount: data.amount,
        description: data.description,
      });

      // Create income record if it doesn't exist
      await prisma.income.upsert({
        where: { importHash },
        update: {}, // No update if exists
        create: {
          amount: new Prisma.Decimal(data.amount),
          description: data.description,
          date: data.date,
          categoryId: defaultCategory.id,
          userId,
          accountId: targetAccountId,
          type: "CREDIT",
          categoryValidated: true,
          suggestedCategoryId: data.suggestedCategoryId,
          importHash,
        },
      });
    }

    return { success: true };
  } catch (error) {
    console.error("Error creating income records:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create income records",
    };
  }
}

// --- 將小額收入依金額歸類並匯入 bankAccount.income ---
async function createIncomeFromSmallDeposits(
  depositsData: Array<{
    transactionDate: string;
    type: "DEBIT" | "CREDIT";
    remark: string;
    amount: string | number;
    row: CsvRow;
  }>,
  accountId: string
): Promise<{ success: boolean; error?: string; unmatched?: any[] }> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("未經授權");

    // Account mapping based on pattern
    const managementAccountId = process.env.VIRTUAL_ACCOUNT_ID_MANAGEMENT;
    const parkingAccountId = process.env.VIRTUAL_ACCOUNT_ID_CAR_PARKING_SPACE;

    if (!managementAccountId || !parkingAccountId) {
      throw new Error(
        "Virtual account IDs not configured in environment variables"
      );
    }

    const targetAccountId = accountId.includes("************")
      ? managementAccountId
      : parkingAccountId;

    const unmatchedDeposits: any[] = [];
    
    // Create accumulators for each description type
    const accumulatedDeposits: {
      [key: string]: {
        totalAmount: number;
        date: Date;
        description: string;
        suggestedCategoryId?: string;
        transactions: Array<{
          date: Date;
          amount: number;
          transactionDate: string;
        }>;
      };
    } = {};

    // Separate special deposits from regular small deposits
    const specialDeposits: typeof depositsData = [];
    const regularSmallDeposits: typeof depositsData = [];

    // First pass: Separate special deposits
    for (const deposit of depositsData) {
      const isSpecialDeposit =
        deposit.row.說明.includes("存款息") || // Keep deposit interest info
        deposit.row.備註.includes("信義") || // Keep real estate transactions
        deposit.row.備註.includes("永慶") || // Keep real estate transactions
        deposit.row.備註.includes("全國數位") || // Keep cable media transactions
        deposit.row.交易資訊.includes("168888") || // Keep management committee account
        deposit.row.交易資訊.includes("僑＊福華社區管理委員會"); // Keep management committee name

      // Debug logging for 全國數位 transactions
      if (deposit.row.備註.includes("全國數位")) {
        console.log("[DEBUG] Found 全國數位 transaction:", {
          remark: deposit.row.備註,
          transactionInfo: deposit.row.交易資訊,
          amount: deposit.amount,
          isSpecialDeposit,
          說明: deposit.row.說明
        });
      }

      if (isSpecialDeposit) {
        specialDeposits.push(deposit);
      } else {
        regularSmallDeposits.push(deposit);
      }
    }

    console.log(`Processing ${specialDeposits.length} special deposits and ${regularSmallDeposits.length} regular small deposits`);

    // Process special deposits individually (similar to createIncomeFromDeposits)
    for (const deposit of specialDeposits) {
      const amount = typeof deposit.amount === "string"
        ? parseFloat(deposit.amount.replace(/,/g, ""))
        : deposit.amount;

      if (isNaN(amount)) {
        unmatchedDeposits.push({ ...deposit, reason: "Invalid amount" });
        continue;
      }

      // Select the best description based on available information
      let description = deposit.remark !== "−"
        ? deposit.remark
        : deposit.row.交易資訊 !== "−"
          ? deposit.row.交易資訊.trim()
          : deposit.row.說明; // Fallback to 說明 if both are "−"

      // Rewrite specific descriptions to more readable text
      if (description.includes("全國數位有線電視股份有限公司")) {
        description = "全國數位使用電費";
      }

      // Parse and validate date
      const dateParts = deposit.row.交易日期.split("/");
      if (dateParts.length !== 3) {
        unmatchedDeposits.push({ ...deposit, reason: `Invalid date format: ${deposit.row.交易日期}` });
        continue;
      }

      const [year, month, day] = dateParts;
      const parsedYear = parseInt(year);
      const parsedMonth = parseInt(month) - 1;
      const parsedDay = parseInt(day);

      if (isNaN(parsedYear) || isNaN(parsedMonth) || isNaN(parsedDay)) {
        unmatchedDeposits.push({ ...deposit, reason: `Invalid date components: ${deposit.transactionDate}` });
        continue;
      }

      // Parse time components (Taipei time UTC+8)
      let hours = 0, minutes = 0;
      if (deposit.row.交易日期) {
        const [_, time] = deposit.row.交易日期.split(" ");
        if (time) {
          const [h, m] = time.split(":");
          const parsedHours = parseInt(h);
          const parsedMinutes = parseInt(m);

          if (!isNaN(parsedHours) && !isNaN(parsedMinutes)) {
            hours = parsedHours >= 8 ? parsedHours - 8 : parsedHours + 16;
            minutes = parsedMinutes;
          }
        }
      }

      const date = new Date(Date.UTC(parsedYear, parsedMonth, parsedDay, hours, minutes, 0));

      // Get category suggestion
      const { categoryId: suggestedCategoryId } = await suggestCategory(
        description,
        amount,
        "CREDIT"
      );

      // Create individual income record for special deposit
      const importHash = calculateTransactionFullHash({
        date,
        type: "CREDIT",
        categoryId: suggestedCategoryId || "default",
        accountId: targetAccountId,
        amount,
        description,
      });

      await prisma.income.upsert({
        where: { importHash },
        update: {},
        create: {
          amount: new Prisma.Decimal(amount),
          description,
          date,
          categoryId: suggestedCategoryId,
          userId,
          accountId: targetAccountId,
          type: "CREDIT",
          categoryValidated: true,
          suggestedCategoryId,
          importHash,
        },
      });

      console.log(`Processed special deposit: ${description} - ${amount}`);
    }

    // Second pass: Process regular small deposits with amount-based categorization
    for (const deposit of regularSmallDeposits) {
      const amount = typeof deposit.amount === "string"
        ? parseFloat(deposit.amount.replace(/,/g, ""))
        : deposit.amount;

      if (isNaN(amount)) {
        unmatchedDeposits.push({ ...deposit, reason: "Invalid amount" });
        continue;
      }

      // Determine description based on amount and remark
      let description;
      if (amount % 500 === 0 && amount <= 2500 && !deposit.remark.includes("臨停")) {
        description = "機車自行車清潔費";
        // Note: Motor bike parking data is now collected in processMotorBikeParkingLot function
      } else if (amount < 700 || deposit.remark.includes("磁扣") || deposit.remark.includes("臨停")) {
        description = "臨時停車費／磁扣";
      //} else if (amount % 500 === 0 && amount <= 2500 && amount !== 974 && amount !== 996) {
      } else {
        unmatchedDeposits.push({
          ...deposit,
          reason: "Amount too large for small deposits"
        });
        continue;
      }

      // Parse date parts
      const dateParts = deposit.row.交易日期.split("/");
      if (dateParts.length !== 3) {
        unmatchedDeposits.push({ ...deposit, reason: "Invalid date format" });
        continue;
      }

      const [year, month, day] = dateParts;
      const parsedYear = parseInt(year);
      const parsedMonth = parseInt(month) - 1;
      const parsedDay = parseInt(day);

      if (isNaN(parsedYear) || isNaN(parsedMonth) || isNaN(parsedDay)) {
        unmatchedDeposits.push({ ...deposit, reason: "Invalid date components" });
        continue;
      }

      // Parse time components (Taipei time UTC+8)
      let hours = 0, minutes = 0;
      if (deposit.row.交易日期) {
        const [_, time] = deposit.row.交易日期.split(" ");
        if (time) {
          const [h, m] = time.split(":");
          const parsedHours = parseInt(h);
          const parsedMinutes = parseInt(m);

          if (!isNaN(parsedHours) && !isNaN(parsedMinutes)) {
            hours = parsedHours >= 8 ? parsedHours - 8 : parsedHours + 16;
            minutes = parsedMinutes;
          }
        }
      }

      const date = new Date(Date.UTC(parsedYear, parsedMonth, parsedDay, hours, minutes, 0));

      // Get category suggestion
      const { categoryId: suggestedCategoryId } = await suggestCategory(
        description,
        amount,
        "CREDIT"
      );

      // Accumulate by description
      if (!accumulatedDeposits[description]) {
        accumulatedDeposits[description] = {
          totalAmount: 0,
          date: date, // Use the first transaction's date
          description,
          suggestedCategoryId,
          transactions: []
        };
      }

      accumulatedDeposits[description].totalAmount += amount;
      accumulatedDeposits[description].transactions.push({
        date,
        amount,
        transactionDate: deposit.transactionDate
      });
    }

    // First ensure we have a default category for income
    const defaultCategory = await prisma.category.upsert({
      where: {
        name_userId: {
          name: 'INCOME',
          userId,
        },
      },
      update: {},
      create: {
        name: 'INCOME',
        userId,
        icon: 'Briefcase',
        type: "CREDIT"
      },
    });

    // Process each accumulated deposit
    for (const [description, data] of Object.entries(accumulatedDeposits)) {
      // Sort transactions by date and use the latest date
      const sortedTransactions = data.transactions.sort((a, b) => b.date.getTime() - a.date.getTime());
      const latestTransaction = sortedTransactions[0];

      // Generate deduplication hash using the accumulated data
      const importHash = calculateTransactionFullHash({
        date: latestTransaction.date,
        type: "CREDIT",
        categoryId: defaultCategory.id,
        accountId: targetAccountId,
        amount: data.totalAmount,
        description: description,
      });

      // Create or update the income record
      await prisma.income.upsert({
        where: { importHash },
        update: {}, // No update if exists
        create: {
          amount: new Prisma.Decimal(data.totalAmount),
          description: description,
          date: latestTransaction.date,
          categoryId: defaultCategory.id,
          userId,
          accountId: targetAccountId,
          type: "CREDIT",
          categoryValidated: true,
          suggestedCategoryId: data.suggestedCategoryId,
          importHash,
        },
      });
    }

    return {
      success: true,
      unmatched: unmatchedDeposits,
    };
  } catch (error) {
    console.error("Error creating income records:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to create income records",
      unmatched: [],
    };
  }
}


function calculateMonthlyTotals(records: CsvRow[]): MonthlyTotals {
  const monthlyTotals: MonthlyTotals = {};

  for (const row of records) {
    // Skip rows without transaction date
    if (!row["交易日期"]) continue;

    // Extract year and month from transaction date (format: YYYY/MM/DD)
    const fullDateTimeStr = String(row["交易日期"]);
    const dateStr = fullDateTimeStr.split(/[\s\n]/)[0]; // Get date part only
    const dateParts = dateStr.split('/');
    
    if (dateParts.length < 3) continue; // Need at least year/month/day
    
    const year = parseInt(dateParts[0]);
    const month = parseInt(dateParts[1]);
    
    // Validate year and month
    if (isNaN(year) || isNaN(month) || year < 1900 || year > 2100 || month < 1 || month > 12) {
      console.warn(`Invalid date found: ${row["交易日期"]}, parsed as year: ${year}, month: ${month}`);
      continue;
    }
    
    const yearMonth = `${year}-${String(month).padStart(2, '0')}`;

    // Initialize month entry if it doesn't exist
    if (!monthlyTotals[yearMonth]) {
      monthlyTotals[yearMonth] = {
        deposits: 0,
        withdrawals: 0
      };
    }

    // Add deposit amount
    if (row["存入"] && row["存入"] !== "−") {
      const depositAmount = parseFloat(row["存入"].replace(/,/g, '')) || 0;
      monthlyTotals[yearMonth].deposits += depositAmount;
    }

    // Add withdrawal amount
    if (row["提出"] && row["提出"] !== "−") {
      const withdrawalAmount = parseFloat(row["提出"].replace(/,/g, '')) || 0;
      monthlyTotals[yearMonth].withdrawals += withdrawalAmount;
    }
  }

  return monthlyTotals;
}
// Helper function to find resident by pattern
function findResidentByPattern(transactionInfo: string, remark: string, amount: number): string | null {
  //const textToSearch = `${transactionInfo || ''} ${remark || ''}`;
  const textToSearch = `${transactionInfo || ''}`;

  for (const [residentId, patterns] of RESIDENT_PATTERNS) {
    // Check if any of the patterns match
    const hasMatch = patterns.some(pattern => pattern.test(textToSearch));

    if (hasMatch) {
      // Special cases for amount-dependent matching
      if (residentId === '69-11-1' && amount < 1288) continue;
      if (residentId === '69-11-2' && amount < 1396) continue;
      return residentId;
    }
  }
  return null;
}

// Separate processing functions
function processLastDayBalances(records: CsvRow[]): { [monthYear: string]: MonthBalance } {
  const balances: { [monthYear: string]: MonthBalance } = {};

  console.log("[DEBUG] processLastDayBalances called with", records.length, "records");

  for (const row of records) {
    const transactionDateStr = row["交易日期"];
    if (!transactionDateStr) {
      console.log("[DEBUG] Skipping row - no transaction date:", row);
      continue;
    }

    const fullDateTimeStr = String(transactionDateStr);
    const dateStr = fullDateTimeStr.split(/[\s\n]/)[0];
    const dateParts = dateStr.split("/");

    console.log("[DEBUG] Processing row:", {
      transactionDateStr,
      fullDateTimeStr,
      dateStr,
      dateParts,
      balance: row["餘額"]
    });

    if (dateParts.length !== 3) {
      console.log("[DEBUG] Skipping row - invalid date parts:", dateParts);
      continue;
    }

    const year = parseInt(dateParts[0]);
    const month = parseInt(dateParts[1]);
    const day = parseInt(dateParts[2]);

    if (isNaN(year) || isNaN(month) || isNaN(day)) {
      console.log("[DEBUG] Skipping row - invalid date numbers:", { year, month, day });
      continue;
    }
    
    const monthYearKey = `${year}/${String(month).padStart(2, "0")}`;
    const lastDay = new Date(year, month, 0).getDate();
    const balance = row["餘額"];
    const isLastDayOfMonth = day === lastDay;

    const currentDate = new Date(year, month - 1, day);
    const currentTime = fullDateTimeStr.split(/[\s\n]/)[1];
    console.log("balance", balance, "isLastDayOfMonth", isLastDayOfMonth);
    
    if (currentTime) {
      const [hours, minutes] = currentTime.split(":");
      currentDate.setHours(parseInt(hours), parseInt(minutes));
    }

    const existingBalance = balances[monthYearKey];
    const shouldUpdate = !existingBalance || 
      new Date(existingBalance.fullDateTime) <= currentDate;

    console.log("shouldUpdate===============================>", shouldUpdate);
    console.log("balances", balances);
    console.log("existingBalance", existingBalance);
    console.log("balances[monthYearKey]", balances[monthYearKey]);
    if (shouldUpdate) {
      const balanceNum = typeof balance === "string" 
        ? parseFloat(balance.replace(/,/g, "")) 
        : balance;
        
      if (!isNaN(balanceNum)) {
        balances[monthYearKey] = {
          date: dateStr,
          fullDateTime: fullDateTimeStr,
          balance: balanceNum,
          isLastDayOfMonth,
          rawData: row,
        };
      }
    }
  }

  console.log("[DEBUG] processLastDayBalances returning:", balances);
  console.log("[DEBUG] Number of balance entries found:", Object.keys(balances).length);

  return balances;
}

function processMotorBikeParkingLot(depositRecords: CsvRow[]): Array<{
  transactionDate: string;
  type: "CREDIT";
  amount: number;
  remark: string;
  row: any;
}> {
  const motorBikeParkingData: Array<{
    transactionDate: string;
    type: "CREDIT";
    amount: number;
    remark: string;
    row: any;
  }> = [];

  // Initialize global array if it doesn't exist
  if (!globalThis.motorBikeParkingLot) globalThis.motorBikeParkingLot = [];

  for (const row of depositRecords) {
    const depositAmountStr = row["存入"];
    const remark = row["備註"];
    const transactionDateStr = row["交易日期"];

    if (!depositAmountStr || depositAmountStr.trim() === "−" || !transactionDateStr) {
      continue;
    }

    try {
      const amount = parseFloat(depositAmountStr.replace(/,/g, ""));

      if (isNaN(amount)) continue;

      // Check if this is motor bike parking fee (機車自行車清潔費)
      if (amount % 500 === 0 && amount <= 2500 && !remark.includes("臨停")) {
        const motorBikeEntry = {
          transactionDate: String(transactionDateStr).split(/[\s\n]/)[0],
          type: "CREDIT" as const,
          amount,
          remark: remark || "",
          row
        };

        motorBikeParkingData.push(motorBikeEntry);
        globalThis.motorBikeParkingLot.push(motorBikeEntry);
        console.log("Added motor bike parking entry:##################################", motorBikeEntry);
      }
    } catch (error) {
      console.error("Error processing motor bike parking deposit:", error);
    }
  }

  console.log("Processed motor bike parking data:", motorBikeParkingData.length, "entries");
  return motorBikeParkingData;
}

function processDeposits(
  depositRecords: CsvRow[],
  allCanonicalIdsSet: Set<string>,
  targetYear: number
): { [residentId: string]: { [monthYear: string]: number } } {
  const depositsMaintenanceFee: { [residentId: string]: { [monthYear: string]: number } } = {};
  
  // Initialize global arrays if they don't exist
  if (!globalThis.depositsSmallAmount) globalThis.depositsSmallAmount = [];
  if (!globalThis.depositsSpecial) globalThis.depositsSpecial = [];
  if (!globalThis.depositsUnmatched) globalThis.depositsUnmatched = [];

  for (const row of depositRecords) {
    const depositAmountStr = row["存入"];
    const remark = row["備註"];
    const transactionInfo = row["交易資訊"];
    const transactionDateStr = row["交易日期"];

    if (!depositAmountStr || depositAmountStr.trim() === "−" || !transactionDateStr) {
      continue;
    }

    try {
      const amount = parseFloat(depositAmountStr.replace(/,/g, ""));
      // Check for special deposits first
      const isSpecialDeposit = DEPOSITS_SPECIAL_KEYWORDS.some(keyword =>
        row["備註"]?.includes(keyword) || 
        row["交易資訊"]?.includes(keyword) ||
        row["說明"]?.includes(keyword)
      );
      console.log("isSpecialDeposit=================>", isSpecialDeposit, "row", row);

      if (isSpecialDeposit) {
        globalThis.depositsSpecial.push({
          transactionDate: String(transactionDateStr).split(/[\s\n]/)[0],
          type: "CREDIT",
          amount,
          remark,
          row,
        });
        continue;
      }

      // Debug logging for specific amount
      if (amount === 1472) {
        console.log("[processDeposits] Debug for amount 1472:", {
          amount,
          remark,
          transactionInfo,
          transactionDateStr,
          amountCheck: amount < 974 && amount % 500 !== 0
        });
      }

      if (isNaN(amount) || (amount < 974 && amount % 500 !== 0 && !remark.includes("未入帳補繳")) || (amount % 500 === 0 && remark.includes("臨停") || (remark.includes("臨停費") && !remark.includes("未入帳補繳")) )) {
        // Check if this is a special deposit that should be processed separately
        const isSpecialDeposit =
          row.說明.includes("存款息") || // Keep deposit interest info
          row.備註.includes("信義") || // Keep real estate transactions
          row.備註.includes("永慶") || // Keep real estate transactions
          row.備註.includes("全國數位") || // Keep cable media transactions
          row.交易資訊.includes("168888") || // Keep management committee account
          row.交易資訊.includes("僑＊福華社區管理委員會"); // Keep management committee name

        if (isSpecialDeposit) {
          // Add to depositsUnmatched for special processing
          globalThis.depositsUnmatched.push({
            transactionDate: String(transactionDateStr).split(/[\s\n]/)[0],
            type: "CREDIT",
            amount,
            remark,
            row,
          });
          console.log(`[DEBUG] Added special deposit to depositsUnmatched (processDeposits): ${remark} - ${amount}`);
        } else {
          // Add to depositsSmallAmount for regular small deposit processing
          globalThis.depositsSmallAmount.push({
            transactionDate: String(transactionDateStr).split(/[\s\n]/)[0],
            type: "CREDIT",
            amount,
            remark,
            row,
            reason: "amount < 974",
          });
        }
        continue;
      }

      const dateStr = String(transactionDateStr).split(/[\s\n]/)[0];
      const dateParts = dateStr.split("/");
      
      if (dateParts.length !== 3) continue;

      const year = parseInt(dateParts[0]);
      const monthNum = parseInt(dateParts[1]);

      if (year === targetYear && monthNum >= 1 && monthNum <= 12) {
        const monthAbbr = MONTH_ABBREVIATIONS[monthNum - 1];
        const monthYearKey = `${year}/${monthAbbr}`;
        
        // Try to parse resident ID using the optimized function
        let parsedRid = parseIdFromRemark(remark, allCanonicalIdsSet, amount) ||
                       findResidentByPattern(transactionInfo || '', remark || '', amount);

        // Debug logging for specific amount
        if (amount === 1472) {
          console.log("[processDeposits] Parsing result for amount 1472:", {
            remark,
            transactionInfo,
            parsedRid,
            monthYearKey,
            year,
            targetYear,
            monthNum
          });
        }

        if (parsedRid) {
          if (!depositsMaintenanceFee[parsedRid]) {
            depositsMaintenanceFee[parsedRid] = {};
          }
          depositsMaintenanceFee[parsedRid][monthYearKey] =
            (depositsMaintenanceFee[parsedRid][monthYearKey] || 0) + amount;

          console.log("Added deposit from resident:", parsedRid, monthYearKey, amount);
        } else {
          // Debug logging for specific amount
          if (amount === 1472) {
            console.log("[processDeposits] Adding to depositsUnmatched - amount 1472:", {
              remark,
              transactionInfo,
              dateStr,
              reason: "No resident ID parsed"
            });
          }

          globalThis.depositsUnmatched.push({
            transactionDate: dateStr,
            type: "CREDIT",
            amount,
            remark,
            row,
          });
        }
      }
    }
    catch (error) {
      console.error("Error processing deposit row:", row, error);
    }
  }
  
  return depositsMaintenanceFee;
}

function processWithdrawals(withdrawalRecords: CsvRow[]): Array<{
  transactionDate: string;
  type: "DEBIT";
  remark: string;
  amount: string | number;
  row: CsvRow;
}> {
  const withdrawalsData: Array<{
    transactionDate: string;
    type: "DEBIT";
    remark: string;
    amount: string | number;
    row: CsvRow;
  }> = [];

  // Initialize global arrays if they don't exist
  if (!globalThis.withdrawals) globalThis.withdrawals = [];
  if (!globalThis.withdrawalsUnmatched) globalThis.withdrawalsUnmatched = [];

  // Map to accumulate special withdrawals by date and keyword
  const specialWithdrawalAccumulator = new Map<string, {
    transactionDate: string;
    type: "DEBIT";
    remark: string;
    amount: number;
    rows: CsvRow[];
    count: number;
  }>();

  for (const row of withdrawalRecords) {
    const withdrawAmountStr = row["提出"];
    const remark = row["備註"];
    const transactionInfo = row["交易資訊"];
    const transactionDateStr = row["交易日期"];

    if (!withdrawAmountStr || withdrawAmountStr.trim() === "−" || !transactionDateStr) {
      continue;
    }

    try {
      const dateStr = String(transactionDateStr).split(/[\s\n]/)[0];
      const dateParts = dateStr.split("/");
      
      if (dateParts.length !== 3) continue;
      
      const year = parseInt(dateParts[0]);
      const monthNum = parseInt(dateParts[1]);
      
      if (isNaN(year) || monthNum < 1 || monthNum > 12) continue;

      const amount = parseFloat(withdrawAmountStr.replace(/,/g, ""));

      // Check if this is a special withdrawal
      const textToSearch = `${remark || ''} ${transactionInfo || ''}`;

      // Debug logging for the specific case
      if (textToSearch.includes("0000025035077777")) {
        console.log("[DEBUG] Found 0000025035077777 in transaction:", {
          remark,
          transactionInfo,
          textToSearch,
          withdrawAmountStr,
          dateStr
        });
      }

      let matchedKeyword = WITHDRAWALS_SPECIAL_KEYWORDS.find(keyword =>
        textToSearch.includes(keyword)
      );

      // Special conditions for blank remark and transactionInfo
      // Check for blank fields first
      if (remark.trim() === "−" && transactionInfo.trim() === "−") {
        // Only match amount when both fields are blank
        if (amount === 134000) {
          matchedKeyword = "東聯";
        } else if (amount === 62850) {
          matchedKeyword = "日立";
        } else if (amount === 31000) {
          matchedKeyword = "清潔";
        } else if (amount === 4000) {
          matchedKeyword = "冠福";
        }
      } else {
        // Check non-blank remarks for keyword matches
        if (remark.includes("東聯") || remark.includes("保全")) {
          matchedKeyword = "東聯";
        } else if (remark.includes("日立") || remark.includes("電梯")) {
          matchedKeyword = "日立";
        } else if (remark.includes("清潔") || remark.includes("清理")) {
          matchedKeyword = "清潔";
        } else if (remark.includes("冠福") || remark.includes("機電")) {
          matchedKeyword = "冠福";
        }
      }

      // Debug logging for matching result
      console.log("textToSearch", textToSearch, "matchedKeyword", matchedKeyword)
      if (textToSearch.includes("0000025035077777")) {
        console.log("[DEBUG] Matching result for 0000025035077777:", {
          matchedKeyword,
          allKeywords: WITHDRAWALS_SPECIAL_KEYWORDS
        });
      }

      if (matchedKeyword) {
        // Special withdrawal - accumulate by date and keyword
        const accumulatorKey = `${dateStr}_${matchedKeyword}`;

        // Rewrite specific keywords to full names
        let displayRemark = matchedKeyword;
        if (matchedKeyword.includes("0000025035077777")) {
          displayRemark = "轉汽車專戶7777-7";
        } else if (matchedKeyword === "東聯" || matchedKeyword.includes("東聯")) {
          displayRemark = "東聯保全費";
        } else if (matchedKeyword === "清潔" || matchedKeyword.includes("清潔")) {
          displayRemark = "社區清潔費";
        } else if (matchedKeyword === "冠福" || matchedKeyword.includes("冠福")) {
          displayRemark = "冠福機電保養費";
        } else if (matchedKeyword === "日立" || matchedKeyword.includes("日立")) {
          displayRemark = "日立永大電梯保養費";
        } else if (matchedKeyword.includes("強迅")) {
          displayRemark = "強迅監視系統維修費";
        }

        if (specialWithdrawalAccumulator.has(accumulatorKey)) {
          // Add to existing entry
          const existing = specialWithdrawalAccumulator.get(accumulatorKey)!;
          existing.amount += isNaN(amount) ? 0 : amount;
          existing.rows.push(row);
          existing.count += 1;

          console.log(`[DEBUG] Accumulating ${displayRemark} on ${dateStr}: ${existing.amount} (count: ${existing.count})`);
        } else {
          // Create new entry
          specialWithdrawalAccumulator.set(accumulatorKey, {
            transactionDate: dateStr,
            type: "DEBIT" as const,
            remark: displayRemark,
            amount: isNaN(amount) ? 0 : amount,
            rows: [row],
            count: 1
          });

          console.log(`[DEBUG] New ${displayRemark} entry on ${dateStr}: ${isNaN(amount) ? 0 : amount}`);
        }
      } else {
        // Unmatched withdrawal - add directly
        const withdrawalData = {
          transactionDate: dateStr,
          type: "DEBIT" as const,
          remark: remark || "",
          amount: isNaN(amount) ? withdrawAmountStr : amount,
          row,
        };
        globalThis.withdrawalsUnmatched.push(withdrawalData);
      }
    } catch (error) {
      console.error("Error processing withdrawal row:", row, error);
    }
  }

  // Process accumulated special withdrawals
  for (const [, accumulated] of specialWithdrawalAccumulator.entries()) {
    const withdrawalData = {
      transactionDate: accumulated.transactionDate,
      type: "DEBIT" as const,
      remark: accumulated.count > 1
        ? `${accumulated.remark} (${accumulated.count}筆)`
        : accumulated.remark,
      amount: accumulated.amount,
      row: accumulated.rows[0], // Use the first row as representative
    };

    withdrawalsData.push(withdrawalData);
    globalThis.withdrawals.push(withdrawalData);

    console.log(`[DEBUG] Final accumulated withdrawal: ${accumulated.remark} on ${accumulated.transactionDate} = ${accumulated.amount} (${accumulated.count} transactions)`);
  }

  return withdrawalsData;
}