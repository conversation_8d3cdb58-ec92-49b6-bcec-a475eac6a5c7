"use client";
import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, PrinterIcon, ListFilter } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Loading } from "@/components/ui/loader";

type PaymentTicket = {
  seq: number;
  household: string;
  residentId: string;
  name: string;
  area: number;
  paymentMethod: string;
  combined: string;
  parkingNo: string;
  carFee: number;
  managementFee: number;
  motorcycleFee: number;
  total: number;
};

const formatZeroAsBlank = (value: number) => {
  if (value === 0) return "";
  return value.toLocaleString();
};

const formatCarFee = (value: number, combined: string) => {
  if (combined === "N" || value === 0) return "";
  return value.toLocaleString();
};

const Ticket = ({ payment, year, month }: { payment: PaymentTicket, year: number, month: number }) => {
  return (
    <div className="bg-white py-6 font-serif" style={{ border: '0px solid #ccc', breakInside: 'avoid-page' }}>
      <div className="text-center font-stretch-ultra-expanded">
        <h1 className="text-3xl font-bold">僑星福華社區管理委員會 {year}年{month}月份管理費收繳單</h1>
      </div>

      <div className="mt-4 col-span-4 text-right text-md px-2">序號：{String(payment.seq).padStart(4, '0')}</div>
      <div className="grid grid-cols-12 gap-x-4 text-lg">
        <div className="col-span-4">戶別：{payment.household}</div>
        <div className="col-span-4">住戶編號：{payment.residentId}</div>        
        <div className="col-span-4 flex items-center justify-end text-right">姓名：{payment.name || <Ellipsis size={18} className="mx-2.5 text-gray-400"/>}</div>
        <div className="col-span-12 flex items-center mt-4 font-bold text-xl text-right gap-x-6">
          <div className="col-span-6 flex items-center justify-start">戶名：僑星福華社區管理委員會</div>
          <div className="col-span-3 flex items-center justify-end">帳號：025035168888</div>
          <div className="col-span-3 flex items-center text-sm">(國泰世華銀行018 新莊分行)</div> 
        </div>

      </div>

      <div className="mt-4 border-2 border-black text-xl">
        <div className="grid grid-cols-12" style={{ height: '180px' }}>
          {/* 收費項目 column */}
          <div className="col-span-1 border-r-2 border-black flex items-center justify-center font-bold" style={{ writingMode: 'vertical-rl' }}>
            收費項目
          </div>
          
          {/* Main content area */}
          {/* Main content area */}
          <div className="col-span-9">
            {/* Row 1: 管理費 */}
            <div className="grid grid-cols-6 border-b-2 border-black" style={{ height: '45px' }}>
              <div className="col-span-2 flex items-center p-2">管理費</div>
              <div className="col-span-2 border-x-2 border-black flex items-center justify-end p-2">{formatZeroAsBlank(payment.managementFee)}</div>
              <div className="col-span-2 flex items-center p-2">坪數：{formatZeroAsBlank(payment.area)}</div>
            </div>
            
            {/* Row 2: 汽車管理費 */}
            <div className="grid grid-cols-6 border-b-2 border-black" style={{ height: '45px' }}>
              <div className="col-span-2 flex items-center p-2">汽車管理費</div>
              <div className="col-span-2 border-x-2 border-black flex items-center justify-end p-2">{formatCarFee(payment.carFee, payment.combined)}</div>
              <div className="col-span-2 flex items-center p-2">車位號碼：{payment.parkingNo}</div>
            </div>
            
            {/* Row 3: 機車管理費 */}
            <div className="grid grid-cols-6 border-b-2 border-black" style={{ height: '45px' }}>
              <div className="col-span-2 flex items-center p-2">機車管理費</div>
              <div className="col-span-2 border-x-2 border-black flex items-center justify-end p-2">{formatZeroAsBlank(payment.motorcycleFee)}</div>
              <div className="col-span-2 flex items-center p-2"></div>
            </div>
            
            {/* Row 4: 應繳金額 */}
            <div className="grid grid-cols-6" style={{ height: '45px' }}>
              <div className="col-span-2 flex items-center p-2 text-2xl font-bold">應繳金額</div>
              <div className="col-span-2 border-x-2 border-black flex items-center justify-end p-2 text-2xl font-bold">NT${formatZeroAsBlank(payment.total)}</div>
              <div className="col-span-2 flex items-center p-2"></div>
            </div>
          </div>
          
          {/* 經手人/註記 column */}
          <div className="col-span-2 border-l-2 border-black flex flex-col items-center justify-between text-base p-2">
            <div className="self-start">經手人/註記</div>
            <div className="text-center">
              {payment.residentId} ({year}/{month})
            </div>
            <div></div>
          </div>
        </div>
      </div>
    </div>
  );
};

const chunk = <T,>(arr: T[], size: number): T[][] => {
    return Array.from({ length: Math.ceil(arr.length / size) }, (v, i) =>
      arr.slice(i * size, i * size + size)
    );
};

export default function PaymentsPage() {
  const [pages, setPages] = useState<PaymentTicket[][]>([]);
  const [loading, setLoading] = useState(true);
  const [year, setYear] = useState(new Date().getFullYear() - 1911);
  const [month, setMonth] = useState(new Date().getMonth() + 2);
  const [allPayments, setAllPayments] = useState<PaymentTicket[]>([]);
  const [selectedMethods, setSelectedMethods] = useState<string[]>(["現金", "轉帳"]);

  useEffect(() => {
    const getPayments = async () => {
      setLoading(true);
      try {
        const response = await fetch('/api/payments/sheets');
        if (!response.ok) {
          throw new Error('Failed to fetch payments');
        }
        const payments = await response.json();
        setAllPayments(payments);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };
    getPayments();
  }, []);

  useEffect(() => {
    console.log("allPayments: ", allPayments);
    console.log("selectedMethods: ", selectedMethods);
    const filtered = allPayments.filter(p => selectedMethods.includes(p.paymentMethod));
    setPages(chunk(filtered, 2));
  }, [allPayments, selectedMethods]);

  const handlePrint = () => {
    window.print();
  };


  if (loading) {
    return <div className="flex justify-center items-center h-screen p-8"><Loading /></div>;
  }

  return (
    <div className="bg-gray-200 print:bg-white w-full p-4 sm:p-8 print:p-0">
      <style jsx global>{`
        @media print {
          body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          .page-container {
            page-break-after: always;
            margin: 0;
            box-shadow: none;
          }
          .no-print {
            display: none;
          }
        }
        @page {
          size: A4;
          margin: 0;
        }
      `}</style>
      <div className="max-w-5xl mx-auto">
        <div className="no-print mb-4 flex items-end space-x-4">
            <div>
                <label htmlFor="year-input" className="block text-sm font-medium text-gray-700">年度 (民國)</label>
                <input id="year-input" type="number" value={year} onChange={(e) => setYear(Number(e.target.value))} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2" />
            </div>
            <div>
                <label htmlFor="month-input" className="block text-sm font-medium text-gray-700">月份</label>
                <input id="month-input" type="number" value={month} onChange={(e) => setMonth(Number(e.target.value))} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2" />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <ListFilter className="mr-2 h-4 w-4" />
                  Filter by Payment Method
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>繳費方式</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {["現金", "轉帳", "自動扣款"].map((method) => (
                  <DropdownMenuCheckboxItem
                    key={method}
                    checked={selectedMethods.includes(method)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedMethods([...selectedMethods, method]);
                      } else {
                        setSelectedMethods(selectedMethods.filter(m => m !== method));
                      }
                    }}
                  >
                    {method}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
            <div>
                <Button onClick={handlePrint}>
                    <PrinterIcon className="mr-2 h-4 w-4" />
                    Print
                </Button>
            </div>
        </div>
        <h1 className="text-2xl font-bold mb-4 no-print">繳費單預覽</h1>
        <p className="mb-4 no-print">每頁顯示兩張繳費單，可直接列印。</p>
          {pages.map((pagePayments, pageIndex) => (
            <div key={pageIndex} className="page-container bg-white mb-8 print-mb-0 flex flex-col" style={{ width: '210mm', height: '297mm', margin: '0px auto', padding: '5mm', boxSizing: 'border-box' }}>
              
              {/* First ticket container */}
              <div className="mt-[-1.1rem] flex-1 flex items-center justify-center">
                {pagePayments[0] && <Ticket payment={pagePayments[0]} year={year} month={month} />}
              </div>
              
              {/* Separator */}
              <div className="border-b-2 border-dashed border-gray-400"></div>
              
              {/* Second ticket container */}
              <div className="flex-1 flex items-center justify-center">
                {pagePayments[1] && <Ticket payment={pagePayments[1]} year={year} month={month} />}
              </div>
              
            </div>
          ))}
      </div>
    </div>
  );
}