import { ItemType as PrismaItemType, Sale, SaleItem, Inventory, Resident } from '../../prisma/app/generated/prisma/client';

// Re-export ItemType for easy import in other frontend files
export const ItemType = PrismaItemType;
export type ItemType = PrismaItemType;


// Type for inventory data returned from the server
export interface InventoryData {
  itemType: ItemType;
  current: number;
  price: number;
}

// Type for form data when creating a new sale
export interface SaleFormData {
  date: string;
  residentName: string;
  address: string;
  receiptNo: string;
  itemType: ItemType;
  quantity: number;
  unitPrice: number;
  recipient: string;
}

// Type for sales history records displayed in the UI
// This should match the return type of getSalesHistory
export type SaleRecord = Sale & {
  saleItems: (SaleItem & {
    inventory: Inventory;
  })[];
  resident: Resident | null;
};

// Convert ItemType enum for display
export const ItemTypeDisplay: Record<ItemType, string> = {
  A_BUILDING_CARD: 'A棟磁扣',
  B_BUILDING_CARD: 'B棟磁扣',
  ALL_AREA_CARD: '全區磁扣',
  MAIN_GATE_REMOTE_CONTROL: '大門遙控器'
};
